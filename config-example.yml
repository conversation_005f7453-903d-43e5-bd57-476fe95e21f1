# ChatCharge 配置示例文件
# 复制此文件为 application-prod.yml 用于生产环境

server:
  port: 8080

spring:
  application:
    name: ChatCharge
  
  # 生产环境建议使用外部数据库
  datasource:
    # H2 数据库配置（开发/测试）
    url: jdbc:h2:file:./data/chatcharge;AUTO_SERVER=TRUE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
    
    # MySQL 数据库配置示例（生产环境推荐）
    # url: ******************************************************************************************
    # driver-class-name: com.mysql.cj.jdbc.Driver
    # username: chatcharge
    # password: your-password
    
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB

logging:
  level:
    com.chatcharge: INFO
    org.springframework.web: WARN
  file:
    name: logs/chatcharge.log

# 应用配置
app:
  chat:
    history:
      max-records: 10
      csv-file: ./data/chat_history.csv
    security:
      enable-html-sanitization: true
      max-message-length: 10000
  database:
    data-directory: ./data/
    excel-import-directory: ./data/excel/

# LLM模型配置
llm:
  models:
    - name: "DeepSeek-V3"
      displayName: "DeepSeek V3"
      apiUrl: "https://api.deepseek.com/v1/chat/completions"
      apiKey: "${DEEPSEEK_API_KEY:your-deepseek-api-key}"
      defaultParams:
        temperature: 0.7
        maxTokens: 4000
        topP: 0.9
        frequencyPenalty: 0.0
        presencePenalty: 0.0
      supportThinking: true
      
    - name: "Qwen3-Turbo"
      displayName: "Qwen 3 Turbo"
      apiUrl: "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
      apiKey: "${QWEN_API_KEY:your-qwen-api-key}"
      defaultParams:
        temperature: 0.7
        maxTokens: 4000
        topP: 0.9
        frequencyPenalty: 0.0
        presencePenalty: 0.0
      supportThinking: true
      
    - name: "GPT-4"
      displayName: "GPT-4"
      apiUrl: "https://api.openai.com/v1/chat/completions"
      apiKey: "${OPENAI_API_KEY:your-openai-api-key}"
      defaultParams:
        temperature: 0.7
        maxTokens: 4000
        topP: 0.9
        frequencyPenalty: 0.0
        presencePenalty: 0.0
      supportThinking: false

  defaultModel: "DeepSeek-V3"
  requestTimeout: 60000
  maxRetries: 3

# 系统提示词配置
system-prompts:
  database-assistant: |
    你是一个专业的数据库查询助手。你的任务是帮助用户将自然语言问题转换为准确的SQL查询语句。
    
    请遵循以下规则：
    1. 仔细分析用户的问题，理解他们想要查询的数据
    2. 根据提供的数据库表结构，生成正确的SQL查询语句
    3. 只生成SELECT查询语句，不要生成INSERT、UPDATE、DELETE等修改数据的语句
    4. 确保SQL语法正确，使用标准SQL语法
    5. 如果问题不清楚，请要求用户提供更多信息
    6. 在生成SQL之前，简要解释你的理解和查询思路
    
    数据库表结构信息：
    {table_structures}
    
    请根据用户的问题生成相应的SQL查询语句。

  general-chat: |
    你是一个友好、专业的AI助手。请用中文回答用户的问题。
    
    请遵循以下原则：
    1. 提供准确、有用的信息
    2. 保持友好和专业的语调
    3. 如果不确定答案，请诚实地说明
    4. 尽量提供详细和有条理的回答
    5. 注意信息安全，不要泄露敏感信息

prompt-templates:
  sql-query-template: |
    基于以下数据库表结构：
    
    {table_structures}
    
    用户问题：{user_question}
    
    请生成相应的SQL查询语句，并简要说明查询逻辑。

  data-analysis-template: |
    基于以下数据查询结果：
    
    {query_results}
    
    用户问题：{user_question}
    
    请分析这些数据并提供洞察。

default:
  system-prompt: "general-chat"
  max-prompt-length: 4000
  enable-context-injection: true
