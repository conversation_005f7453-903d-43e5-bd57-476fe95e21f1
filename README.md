# ChatCharge - LLM聊天应用

一个功能完整的LLM聊天应用，基于Spring Boot + Thymeleaf构建，支持多种LLM模型、数据库查询、Excel数据导入等功能。

## 功能特性

### 🤖 多模型支持
- **DeepSeek V3**: 支持思考过程显示
- **Qwen 3 Turbo**: 支持思考过程显示  
- **GPT-4**: OpenAI的强大模型
- **Claude 3.5 Sonnet**: Anthropic的先进模型

### 💬 智能聊天
- 多轮对话支持
- 思考内容特殊显示（灰色背景）
- 参数可调节（Temperature、Max Tokens、Top P）
- 聊天历史自动保存（最多10轮）

### 🛡️ 信息安全
- HTML内容转义和XSS防护
- 危险代码过滤
- SQL注入防护
- 输入长度限制

### 📊 数据库查询
- 自然语言转SQL查询
- 安全的SELECT查询执行
- 查询结果可视化展示
- 数据分析和洞察生成

### 📈 Excel数据处理
- Excel文件导入到数据库
- 支持.xls和.xlsx格式
- 自动表结构生成
- 数据类型智能推断

### 🔧 系统功能
- 聊天历史导出（CSV格式）
- 数据库表结构查看
- DDL语句生成
- 系统提示词管理

## 技术栈

- **后端**: Spring Boot 3.2.0, Spring WebFlux
- **前端**: Thymeleaf, Bootstrap 5, JavaScript
- **数据库**: H2 Database (可配置其他数据库)
- **文件处理**: Apache POI (Excel), OpenCSV
- **安全**: JSoup (HTML清理)
- **HTTP客户端**: WebClient (响应式)

## 快速开始

### 1. 环境要求
- Java 17+
- Maven 3.6+

### 2. 配置API密钥
在环境变量中设置API密钥：
```bash
export DEEPSEEK_API_KEY=your-deepseek-api-key
export QWEN_API_KEY=your-qwen-api-key
export OPENAI_API_KEY=your-openai-api-key
export CLAUDE_API_KEY=your-claude-api-key
```

或者在`application.yml`中直接配置：
```yaml
llm:
  models:
    - name: "DeepSeek-V3"
      apiKey: "your-actual-api-key"
```

### 3. 运行应用
```bash
# 克隆项目
git clone <repository-url>
cd chatCharge

# 编译运行
mvn spring-boot:run
```

### 4. 访问应用
打开浏览器访问: http://localhost:8080

## 使用指南

### 普通聊天模式
1. 选择左侧的模型
2. 调整参数（可选）
3. 在右侧输入框输入消息
4. 按Ctrl+Enter或点击发送按钮

### 数据库查询模式
1. 切换到"数据库查询"模式
2. 先导入Excel数据（可选）
3. 用自然语言描述查询需求
4. 系统会自动生成SQL并执行
5. 查看查询结果和分析

### Excel数据导入
1. 点击"选择文件"选择Excel文件
2. 输入表名
3. 点击"导入数据"
4. 等待导入完成

## 项目结构

```
src/main/java/com/chatcharge/
├── config/          # 配置类
├── controller/      # 控制器
├── model/          # 数据模型
├── security/       # 安全工具
├── service/        # 业务服务
└── util/           # 工具类

src/main/resources/
├── static/         # 静态资源
├── templates/      # Thymeleaf模板
└── application.yml # 配置文件
```

## 配置说明

### 模型配置
在`application.yml`中配置LLM模型：
```yaml
llm:
  models:
    - name: "模型名称"
      displayName: "显示名称"
      apiUrl: "API地址"
      apiKey: "API密钥"
      defaultParams:
        temperature: 0.7
        maxTokens: 4000
      supportThinking: true
```

### 安全配置
```yaml
app:
  chat:
    security:
      enable-html-sanitization: true
      max-message-length: 10000
```

### 数据库配置
```yaml
spring:
  datasource:
    url: jdbc:h2:file:./data/chatcharge
    username: sa
    password: 
```

## API接口

### 聊天接口
- `POST /api/chat` - 发送聊天消息
- `GET /api/chat/history/{sessionId}` - 获取聊天历史
- `DELETE /api/chat/history/{sessionId}` - 清除聊天历史

### 数据库接口
- `GET /api/database/summary` - 获取数据库摘要
- `POST /api/database/query` - 执行SQL查询
- `POST /api/query/natural-language` - 自然语言查询

### Excel接口
- `POST /api/excel/import` - 导入Excel文件
- `POST /api/excel/info` - 获取Excel文件信息

## 开发指南

### 添加新的LLM模型
1. 在`application.yml`中添加模型配置
2. 在`LlmService`中添加对应的API调用逻辑
3. 更新前端模型选择列表

### 自定义安全规则
在`SecurityUtils`类中修改安全过滤规则：
```java
public String sanitizeUserInput(String input) {
    // 添加自定义安全规则
}
```

### 扩展数据库功能
在`DatabaseUtils`类中添加新的数据库操作方法。

## 测试

运行测试：
```bash
mvn test
```

测试覆盖主要功能：
- 配置加载测试
- 安全工具测试
- Excel处理测试
- 聊天历史测试

## 部署

### Docker部署
```dockerfile
FROM openjdk:17-jdk-slim
COPY target/chat-charge-0.0.1-SNAPSHOT.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

### 生产环境配置
- 使用外部数据库（MySQL/PostgreSQL）
- 配置日志级别
- 设置JVM参数
- 配置反向代理

## 常见问题

### Q: 如何更换数据库？
A: 修改`application.yml`中的数据源配置，并添加相应的数据库驱动依赖。

### Q: 思考内容不显示？
A: 确保模型配置中`supportThinking`设置为`true`，且LLM返回包含`<thinking>`标签。

### Q: Excel导入失败？
A: 检查文件格式是否为.xls或.xlsx，确保文件没有损坏。

## 贡献

欢迎提交Issue和Pull Request！

## 许可证

MIT License
