<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatCharge - LLM聊天助手</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" th:href="@{/css/style.css}">
</head>
<body>
    <div class="container-fluid h-100">
        <div class="row h-100">
            <!-- 左侧配置面板 -->
            <div class="col-md-3 bg-light border-end h-100 overflow-auto">
                <div class="p-3">
                    <h5 class="mb-3">
                        <i class="fas fa-cog"></i> 模型配置
                    </h5>
                    
                    <!-- 模型选择 -->
                    <div class="mb-3">
                        <label for="modelSelect" class="form-label">选择模型</label>
                        <select class="form-select" id="modelSelect">
                            <option th:each="model : ${models}" 
                                    th:value="${model.name}" 
                                    th:text="${model.displayName}"
                                    th:selected="${model.name == defaultModel}">
                            </option>
                        </select>
                    </div>
                    
                    <!-- 参数配置 -->
                    <div class="mb-3">
                        <label for="temperature" class="form-label">
                            Temperature: <span id="temperatureValue">0.7</span>
                        </label>
                        <input type="range" class="form-range" id="temperature" 
                               min="0" max="2" step="0.1" value="0.7">
                    </div>
                    
                    <div class="mb-3">
                        <label for="maxTokens" class="form-label">
                            Max Tokens: <span id="maxTokensValue">4000</span>
                        </label>
                        <input type="range" class="form-range" id="maxTokens" 
                               min="100" max="8000" step="100" value="4000">
                    </div>
                    
                    <div class="mb-3">
                        <label for="topP" class="form-label">
                            Top P: <span id="topPValue">0.9</span>
                        </label>
                        <input type="range" class="form-range" id="topP" 
                               min="0" max="1" step="0.1" value="0.9">
                    </div>
                    
                    <!-- 聊天模式选择 -->
                    <div class="mb-3">
                        <label class="form-label">聊天模式</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="chatMode" 
                                   id="generalChat" value="general" checked>
                            <label class="form-check-label" for="generalChat">
                                普通聊天
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="chatMode" 
                                   id="sqlQuery" value="sql">
                            <label class="form-check-label" for="sqlQuery">
                                数据库查询
                            </label>
                        </div>
                    </div>
                    
                    <!-- 数据库信息 -->
                    <div id="databaseInfo" class="mb-3" style="display: none;">
                        <h6>数据库信息</h6>
                        <div id="dbSummary" class="small text-muted">
                            加载中...
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-primary mt-2" 
                                onclick="loadDatabaseInfo()">
                            <i class="fas fa-refresh"></i> 刷新
                        </button>
                    </div>
                    
                    <!-- 历史记录 -->
                    <div class="mb-3">
                        <h6>聊天历史</h6>
                        <select class="form-select form-select-sm" id="historySelect">
                            <option value="">选择历史会话</option>
                        </select>
                        <div class="mt-2">
                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                    onclick="clearCurrentHistory()">
                                <i class="fas fa-trash"></i> 清除当前
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-info" 
                                    onclick="exportHistory()">
                                <i class="fas fa-download"></i> 导出
                            </button>
                        </div>
                    </div>
                    
                    <!-- Excel导入 -->
                    <div class="mb-3">
                        <h6>Excel数据导入</h6>
                        <input type="file" class="form-control form-control-sm" 
                               id="excelFile" accept=".xlsx,.xls">
                        <button type="button" class="btn btn-sm btn-outline-success mt-2" 
                                onclick="importExcel()">
                            <i class="fas fa-upload"></i> 导入数据
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 右侧聊天区域 -->
            <div class="col-md-9 d-flex flex-column h-100">
                <!-- 聊天标题栏 -->
                <div class="bg-primary text-white p-3">
                    <h4 class="mb-0">
                        <i class="fas fa-comments"></i> ChatCharge
                        <small class="ms-2" id="currentModel">DeepSeek V3</small>
                    </h4>
                </div>
                
                <!-- 聊天消息区域 -->
                <div class="flex-grow-1 overflow-auto p-3" id="chatMessages">
                    <div class="text-center text-muted">
                        <i class="fas fa-robot fa-3x mb-3"></i>
                        <p>欢迎使用ChatCharge！选择模型并开始对话吧。</p>
                    </div>
                </div>
                
                <!-- 输入区域 -->
                <div class="border-top p-3">
                    <div class="input-group">
                        <textarea class="form-control" id="messageInput" 
                                  placeholder="输入您的消息..." rows="2"></textarea>
                        <button class="btn btn-primary" type="button" id="sendButton" onclick="sendMessage()">
                            <i class="fas fa-paper-plane"></i> 发送
                        </button>
                    </div>
                    <div class="mt-2">
                        <small class="text-muted">
                            按 Ctrl+Enter 快速发送 | 
                            <span id="messageCount">0</span> 条消息
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 加载提示模态框 -->
    <div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 mb-0">正在处理中...</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- SQL结果模态框 -->
    <div class="modal fade" id="sqlResultModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">查询结果</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="sqlResultContent"></div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script th:src="@{/js/app.js}"></script>
</body>
</html>
