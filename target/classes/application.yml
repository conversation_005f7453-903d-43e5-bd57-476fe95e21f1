server:
  port: 8080

spring:
  application:
    name: ChatCharge
  
  datasource:
    url: jdbc:h2:file:./data/chatcharge;AUTO_SERVER=TRUE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
    
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
        
  h2:
    console:
      enabled: true
      path: /h2-console
      
  thymeleaf:
    cache: false
    encoding: UTF-8
    mode: HTML
    prefix: classpath:/templates/
    suffix: .html

logging:
  level:
    com.chatcharge: DEBUG
    org.springframework.web: DEBUG
    
# Custom application properties
app:
  chat:
    history:
      max-records: 10
      csv-file: ./data/chat_history.csv
    security:
      enable-html-sanitization: true
      max-message-length: 10000
  database:
    data-directory: ./data/
    excel-import-directory: ./data/excel/
