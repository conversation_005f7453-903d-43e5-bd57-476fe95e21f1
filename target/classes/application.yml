server:
  port: 8080

spring:
  application:
    name: ChatCharge
  
  datasource:
    url: jdbc:h2:file:./data/chatcharge;AUTO_SERVER=TRUE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
    
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
        
  h2:
    console:
      enabled: true
      path: /h2-console
      
  thymeleaf:
    cache: false
    encoding: UTF-8
    mode: HTML
    prefix: classpath:/templates/
    suffix: .html

logging:
  level:
    com.chatcharge: DEBUG
    org.springframework.web: DEBUG
    
# Custom application properties
app:
  chat:
    history:
      max-records: 10
      csv-file: ./data/chat_history.csv
    security:
      enable-html-sanitization: true
      max-message-length: 10000
  database:
    data-directory: ./data/
    excel-import-directory: ./data/excel/

# LLM Model Configuration
llm:
  models:
    - name: "DeepSeek-V3"
      displayName: "DeepSeek V3"
      apiUrl: "https://api.deepseek.com/v1/chat/completions"
      apiKey: "${DEEPSEEK_API_KEY:your-deepseek-api-key}"
      defaultParams:
        temperature: 0.7
        maxTokens: 4000
        topP: 0.9
        frequencyPenalty: 0.0
        presencePenalty: 0.0
      supportThinking: true

    - name: "Qwen3-Turbo"
      displayName: "Qwen 3 Turbo"
      apiUrl: "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
      apiKey: "${QWEN_API_KEY:your-qwen-api-key}"
      defaultParams:
        temperature: 0.7
        maxTokens: 4000
        topP: 0.9
        frequencyPenalty: 0.0
        presencePenalty: 0.0
      supportThinking: true

    - name: "GPT-4"
      displayName: "GPT-4"
      apiUrl: "https://api.openai.com/v1/chat/completions"
      apiKey: "${OPENAI_API_KEY:your-openai-api-key}"
      defaultParams:
        temperature: 0.7
        maxTokens: 4000
        topP: 0.9
        frequencyPenalty: 0.0
        presencePenalty: 0.0
      supportThinking: false

    - name: "Claude-3.5-Sonnet"
      displayName: "Claude 3.5 Sonnet"
      apiUrl: "https://api.anthropic.com/v1/messages"
      apiKey: "${CLAUDE_API_KEY:your-claude-api-key}"
      defaultParams:
        temperature: 0.7
        maxTokens: 4000
        topP: 0.9
        frequencyPenalty: 0.0
        presencePenalty: 0.0
      supportThinking: false

  defaultModel: "DeepSeek-V3"
  requestTimeout: 60000
  maxRetries: 3

# System Prompts Configuration
system-prompts:
  database-assistant: |
    你是一个专业的数据库查询助手。你的任务是帮助用户将自然语言问题转换为准确的SQL查询语句。

    请遵循以下规则：
    1. 仔细分析用户的问题，理解他们想要查询的数据
    2. 根据提供的数据库表结构，生成正确的SQL查询语句
    3. 只生成SELECT查询语句，不要生成INSERT、UPDATE、DELETE等修改数据的语句
    4. 确保SQL语法正确，使用标准SQL语法
    5. 如果问题不清楚，请要求用户提供更多信息
    6. 在生成SQL之前，简要解释你的理解和查询思路

    数据库表结构信息：
    {table_structures}

    请根据用户的问题生成相应的SQL查询语句。

  general-chat: |
    你是一个友好、专业的AI助手。请用中文回答用户的问题。

    请遵循以下原则：
    1. 提供准确、有用的信息
    2. 保持友好和专业的语调
    3. 如果不确定答案，请诚实地说明
    4. 尽量提供详细和有条理的回答
    5. 注意信息安全，不要泄露敏感信息

  code-assistant: |
    你是一个专业的编程助手。请帮助用户解决编程相关的问题。

    请遵循以下原则：
    1. 提供清晰、可运行的代码示例
    2. 解释代码的工作原理
    3. 指出潜在的问题和最佳实践
    4. 支持多种编程语言
    5. 提供代码注释和文档

  data-analyst: |
    你是一个专业的数据分析师。请帮助用户分析和理解数据。

    请遵循以下原则：
    1. 提供清晰的数据分析思路
    2. 使用适当的统计方法
    3. 提供可视化建议
    4. 解释分析结果的含义
    5. 指出数据的局限性和注意事项

prompt-templates:
  sql-query-template: |
    基于以下数据库表结构：

    {table_structures}

    用户问题：{user_question}

    请生成相应的SQL查询语句，并简要说明查询逻辑。

  data-analysis-template: |
    基于以下数据查询结果：

    {query_results}

    用户问题：{user_question}

    请分析这些数据并提供洞察。

default:
  system-prompt: "general-chat"
  max-prompt-length: 4000
  enable-context-injection: true
