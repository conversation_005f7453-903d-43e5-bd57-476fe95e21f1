{"groups": [{"name": "app", "type": "com.chatcharge.config.AppConfig", "sourceType": "com.chatcharge.config.AppConfig"}, {"name": "app.chat", "type": "com.chatcharge.config.AppConfig$ChatConfig", "sourceType": "com.chatcharge.config.AppConfig", "sourceMethod": "public com.chatcharge.config.AppConfig.ChatConfig getChat() "}, {"name": "app.chat.history", "type": "com.chatcharge.config.AppConfig$ChatConfig$HistoryConfig", "sourceType": "com.chatcharge.config.AppConfig$ChatConfig", "sourceMethod": "public com.chatcharge.config.AppConfig.ChatConfig.HistoryConfig getHistory() "}, {"name": "app.chat.security", "type": "com.chatcharge.config.AppConfig$ChatConfig$SecurityConfig", "sourceType": "com.chatcharge.config.AppConfig$ChatConfig", "sourceMethod": "public com.chatcharge.config.AppConfig.ChatConfig.SecurityConfig getSecurity() "}, {"name": "app.database", "type": "com.chatcharge.config.AppConfig$DatabaseConfig", "sourceType": "com.chatcharge.config.AppConfig", "sourceMethod": "public com.chatcharge.config.AppConfig.DatabaseConfig getDatabase() "}, {"name": "llm", "type": "com.chatcharge.config.LlmModelConfig", "sourceType": "com.chatcharge.config.LlmModelConfig"}, {"name": "system-prompts", "type": "com.chatcharge.service.SystemPromptService", "sourceType": "com.chatcharge.service.SystemPromptService"}], "properties": [{"name": "app.chat.history.csv-file", "type": "java.lang.String", "sourceType": "com.chatcharge.config.AppConfig$ChatConfig$HistoryConfig"}, {"name": "app.chat.history.max-records", "type": "java.lang.Integer", "sourceType": "com.chatcharge.config.AppConfig$ChatConfig$HistoryConfig"}, {"name": "app.chat.security.enable-html-sanitization", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.chatcharge.config.AppConfig$ChatConfig$SecurityConfig"}, {"name": "app.chat.security.max-message-length", "type": "java.lang.Integer", "sourceType": "com.chatcharge.config.AppConfig$ChatConfig$SecurityConfig"}, {"name": "app.database.data-directory", "type": "java.lang.String", "sourceType": "com.chatcharge.config.AppConfig$DatabaseConfig"}, {"name": "app.database.excel-import-directory", "type": "java.lang.String", "sourceType": "com.chatcharge.config.AppConfig$DatabaseConfig"}, {"name": "llm.default-model", "type": "java.lang.String", "sourceType": "com.chatcharge.config.LlmModelConfig"}, {"name": "llm.max-retries", "type": "java.lang.Integer", "sourceType": "com.chatcharge.config.LlmModelConfig"}, {"name": "llm.models", "type": "java.util.List<com.chatcharge.config.LlmModelConfig$ModelConfig>", "sourceType": "com.chatcharge.config.LlmModelConfig"}, {"name": "llm.request-timeout", "type": "java.lang.Integer", "sourceType": "com.chatcharge.config.LlmModelConfig"}, {"name": "system-prompts.all-prompt-templates", "type": "java.util.Map<java.lang.String,java.lang.String>", "sourceType": "com.chatcharge.service.SystemPromptService"}, {"name": "system-prompts.all-system-prompts", "type": "java.util.Map<java.lang.String,java.lang.String>", "sourceType": "com.chatcharge.service.SystemPromptService"}, {"name": "system-prompts.default", "type": "java.util.Map<java.lang.String,java.lang.Object>", "sourceType": "com.chatcharge.service.SystemPromptService"}, {"name": "system-prompts.prompt-templates", "type": "java.util.Map<java.lang.String,java.lang.String>", "sourceType": "com.chatcharge.service.SystemPromptService"}, {"name": "system-prompts.system-prompts", "type": "java.util.Map<java.lang.String,java.lang.String>", "sourceType": "com.chatcharge.service.SystemPromptService"}], "hints": []}