/* 全局样式 */
body, html {
    height: 100%;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.h-100 {
    height: 100vh !important;
}

/* 聊天消息样式 */
.message {
    margin-bottom: 1rem;
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.message-user {
    text-align: right;
}

.message-assistant {
    text-align: left;
}

.message-content {
    display: inline-block;
    max-width: 80%;
    padding: 0.75rem 1rem;
    border-radius: 1rem;
    word-wrap: break-word;
}

.message-user .message-content {
    background-color: #007bff;
    color: white;
    border-bottom-right-radius: 0.25rem;
}

.message-assistant .message-content {
    background-color: #f8f9fa;
    color: #333;
    border: 1px solid #dee2e6;
    border-bottom-left-radius: 0.25rem;
}

/* 思考内容样式 */
.thinking-content {
    background-color: #f5f5f5;
    border-left: 4px solid #6c757d;
    padding: 0.5rem 1rem;
    margin: 0.5rem 0;
    font-style: italic;
    color: #6c757d;
    border-radius: 0.25rem;
}

.thinking-content::before {
    content: "💭 思考过程：";
    font-weight: bold;
    display: block;
    margin-bottom: 0.25rem;
}

/* 消息时间戳 */
.message-timestamp {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.message-user .message-timestamp {
    text-align: right;
}

.message-assistant .message-timestamp {
    text-align: left;
}

/* 模型标签 */
.model-badge {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    background-color: #e9ecef;
    color: #495057;
    margin-left: 0.5rem;
}

/* 输入区域样式 */
#messageInput {
    resize: none;
    border-radius: 0.5rem;
}

#messageInput:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

#sendButton {
    border-radius: 0.5rem;
    min-width: 80px;
}

/* 左侧面板样式 */
.bg-light {
    background-color: #f8f9fa !important;
}

.form-range::-webkit-slider-thumb {
    background-color: #007bff;
}

.form-range::-moz-range-thumb {
    background-color: #007bff;
    border: none;
}

/* 滚动条样式 */
.overflow-auto::-webkit-scrollbar {
    width: 6px;
}

.overflow-auto::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.overflow-auto::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.overflow-auto::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* SQL结果表格样式 */
.sql-result-table {
    font-size: 0.875rem;
}

.sql-result-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    border-top: none;
}

.sql-result-table td {
    vertical-align: middle;
}

/* 代码块样式 */
.code-block {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    padding: 1rem;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    overflow-x: auto;
    margin: 0.5rem 0;
}

.sql-code {
    background-color: #e7f3ff;
    border-left: 4px solid #007bff;
}

/* 错误消息样式 */
.error-message {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 0.75rem;
    border-radius: 0.375rem;
    margin: 0.5rem 0;
}

.error-message::before {
    content: "⚠️ ";
    font-weight: bold;
}

/* 成功消息样式 */
.success-message {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
    padding: 0.75rem;
    border-radius: 0.375rem;
    margin: 0.5rem 0;
}

.success-message::before {
    content: "✅ ";
    font-weight: bold;
}

/* 数据库信息样式 */
#databaseInfo {
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 0.75rem;
}

#dbSummary {
    max-height: 150px;
    overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .message-content {
        max-width: 95%;
    }
    
    .col-md-3 {
        position: fixed;
        top: 0;
        left: -100%;
        width: 80%;
        height: 100%;
        z-index: 1050;
        transition: left 0.3s ease;
        background-color: #f8f9fa;
    }
    
    .col-md-3.show {
        left: 0;
    }
    
    .col-md-9 {
        width: 100%;
    }
    
    .mobile-menu-toggle {
        display: block;
        position: fixed;
        top: 1rem;
        left: 1rem;
        z-index: 1051;
        background-color: #007bff;
        color: white;
        border: none;
        border-radius: 0.375rem;
        padding: 0.5rem;
    }
}

@media (min-width: 769px) {
    .mobile-menu-toggle {
        display: none;
    }
}

/* 加载动画 */
.typing-indicator {
    display: inline-block;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #007bff;
    animation: typing 1.4s infinite ease-in-out both;
}

.typing-indicator:nth-child(1) { animation-delay: -0.32s; }
.typing-indicator:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

/* 工具提示样式 */
.tooltip-inner {
    background-color: #333;
    color: white;
    border-radius: 0.375rem;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
}

/* 按钮悬停效果 */
.btn:hover {
    transform: translateY(-1px);
    transition: transform 0.2s ease;
}

.btn:active {
    transform: translateY(0);
}
