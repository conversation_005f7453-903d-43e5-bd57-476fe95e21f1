// 全局变量
let currentSessionId = 'default';
let messageCount = 0;
let isProcessing = false;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupEventListeners();
    loadChatHistory();
    updateParameterDisplays();
});

// 初始化应用
function initializeApp() {
    // 设置当前模型显示
    updateCurrentModelDisplay();
    
    // 加载聊天历史列表
    loadHistoryList();
    
    // 检查聊天模式
    updateChatMode();
}

// 设置事件监听器
function setupEventListeners() {
    // 模型选择变化
    document.getElementById('modelSelect').addEventListener('change', function() {
        updateCurrentModelDisplay();
    });
    
    // 参数滑块变化
    document.getElementById('temperature').addEventListener('input', updateParameterDisplays);
    document.getElementById('maxTokens').addEventListener('input', updateParameterDisplays);
    document.getElementById('topP').addEventListener('input', updateParameterDisplays);
    
    // 聊天模式变化
    document.querySelectorAll('input[name="chatMode"]').forEach(radio => {
        radio.addEventListener('change', updateChatMode);
    });
    
    // 消息输入框键盘事件
    document.getElementById('messageInput').addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 'Enter') {
            sendMessage();
        }
    });
    
    // 历史记录选择
    document.getElementById('historySelect').addEventListener('change', function() {
        if (this.value) {
            loadSpecificHistory(this.value);
        }
    });
}

// 更新参数显示
function updateParameterDisplays() {
    document.getElementById('temperatureValue').textContent = 
        document.getElementById('temperature').value;
    document.getElementById('maxTokensValue').textContent = 
        document.getElementById('maxTokens').value;
    document.getElementById('topPValue').textContent = 
        document.getElementById('topP').value;
}

// 更新当前模型显示
function updateCurrentModelDisplay() {
    const modelSelect = document.getElementById('modelSelect');
    const selectedOption = modelSelect.options[modelSelect.selectedIndex];
    document.getElementById('currentModel').textContent = selectedOption.text;
}

// 更新聊天模式
function updateChatMode() {
    const mode = document.querySelector('input[name="chatMode"]:checked').value;
    const databaseInfo = document.getElementById('databaseInfo');
    
    if (mode === 'sql') {
        databaseInfo.style.display = 'block';
        loadDatabaseInfo();
    } else {
        databaseInfo.style.display = 'none';
    }
}

// 发送消息
async function sendMessage() {
    const messageInput = document.getElementById('messageInput');
    const message = messageInput.value.trim();
    
    if (!message || isProcessing) {
        return;
    }
    
    // 清空输入框
    messageInput.value = '';
    
    // 添加用户消息到界面
    addMessageToChat('user', message);
    
    // 显示加载状态
    showLoadingMessage();
    isProcessing = true;
    updateSendButton(false);
    
    try {
        const chatMode = document.querySelector('input[name="chatMode"]:checked').value;
        
        if (chatMode === 'sql') {
            await sendSqlQuery(message);
        } else {
            await sendChatMessage(message);
        }
    } catch (error) {
        console.error('发送消息失败:', error);
        addErrorMessage('发送消息失败: ' + error.message);
    } finally {
        hideLoadingMessage();
        isProcessing = false;
        updateSendButton(true);
    }
}

// 发送普通聊天消息
async function sendChatMessage(message) {
    const requestData = {
        message: message,
        modelName: document.getElementById('modelSelect').value,
        sessionId: currentSessionId,
        parameters: {
            temperature: parseFloat(document.getElementById('temperature').value),
            maxTokens: parseInt(document.getElementById('maxTokens').value),
            topP: parseFloat(document.getElementById('topP').value)
        }
    };
    
    const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
    });
    
    if (!response.ok) {
        throw new Error('网络请求失败');
    }
    
    const result = await response.json();
    
    // 添加AI回复到界面
    addMessageToChat('assistant', result.content, result.thinkingContent, result.modelName);
}

// 发送SQL查询
async function sendSqlQuery(question) {
    const modelName = document.getElementById('modelSelect').value;
    
    const response = await fetch(`/api/query/natural-language?question=${encodeURIComponent(question)}&model=${modelName}`, {
        method: 'POST'
    });
    
    if (!response.ok) {
        throw new Error('SQL查询失败');
    }
    
    const result = await response.json();
    
    if (result.success) {
        // 显示查询结果
        showSqlResult(result);
        addMessageToChat('assistant', result.analysis || '查询执行成功', null, modelName);
    } else {
        addErrorMessage(result.errorMessage || '查询执行失败');
    }
}

// 添加消息到聊天界面
function addMessageToChat(role, content, thinkingContent = null, modelName = null) {
    const chatMessages = document.getElementById('chatMessages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message message-${role}`;
    
    let messageHtml = '';
    
    if (role === 'user') {
        messageHtml = `
            <div class="message-content">
                ${escapeHtml(content)}
            </div>
            <div class="message-timestamp">
                ${new Date().toLocaleTimeString()}
            </div>
        `;
    } else {
        messageHtml = `
            <div class="message-content">
                ${thinkingContent ? `<div class="thinking-content">${thinkingContent}</div>` : ''}
                ${content}
                ${modelName ? `<span class="model-badge">${modelName}</span>` : ''}
            </div>
            <div class="message-timestamp">
                ${new Date().toLocaleTimeString()}
            </div>
        `;
    }
    
    messageDiv.innerHTML = messageHtml;
    chatMessages.appendChild(messageDiv);
    
    // 滚动到底部
    chatMessages.scrollTop = chatMessages.scrollHeight;
    
    // 更新消息计数
    messageCount++;
    document.getElementById('messageCount').textContent = messageCount;
    
    // 移除欢迎消息
    const welcomeMessage = chatMessages.querySelector('.text-center.text-muted');
    if (welcomeMessage) {
        welcomeMessage.remove();
    }
}

// 显示加载消息
function showLoadingMessage() {
    const chatMessages = document.getElementById('chatMessages');
    const loadingDiv = document.createElement('div');
    loadingDiv.className = 'message message-assistant';
    loadingDiv.id = 'loadingMessage';
    loadingDiv.innerHTML = `
        <div class="message-content">
            <div class="d-flex align-items-center">
                <div class="typing-indicator me-2"></div>
                <div class="typing-indicator me-2"></div>
                <div class="typing-indicator"></div>
                <span class="ms-2">正在思考中...</span>
            </div>
        </div>
    `;
    
    chatMessages.appendChild(loadingDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// 隐藏加载消息
function hideLoadingMessage() {
    const loadingMessage = document.getElementById('loadingMessage');
    if (loadingMessage) {
        loadingMessage.remove();
    }
}

// 添加错误消息
function addErrorMessage(errorText) {
    const chatMessages = document.getElementById('chatMessages');
    const errorDiv = document.createElement('div');
    errorDiv.className = 'message message-assistant';
    errorDiv.innerHTML = `
        <div class="message-content">
            <div class="error-message">
                ${escapeHtml(errorText)}
            </div>
        </div>
        <div class="message-timestamp">
            ${new Date().toLocaleTimeString()}
        </div>
    `;
    
    chatMessages.appendChild(errorDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// 更新发送按钮状态
function updateSendButton(enabled) {
    const sendButton = document.getElementById('sendButton');
    sendButton.disabled = !enabled;
    
    if (enabled) {
        sendButton.innerHTML = '<i class="fas fa-paper-plane"></i> 发送';
    } else {
        sendButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中';
    }
}

// HTML转义
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 显示SQL查询结果
function showSqlResult(result) {
    const modal = new bootstrap.Modal(document.getElementById('sqlResultModal'));
    const content = document.getElementById('sqlResultContent');

    let html = `
        <div class="mb-3">
            <h6>原始问题:</h6>
            <p class="text-muted">${escapeHtml(result.originalQuestion)}</p>
        </div>

        <div class="mb-3">
            <h6>生成的SQL:</h6>
            <div class="code-block sql-code">
                ${escapeHtml(result.generatedSql)}
            </div>
        </div>

        <div class="mb-3">
            <h6>执行时间: ${result.executionTime}ms</h6>
        </div>
    `;

    if (result.data && result.data.length > 0) {
        html += `
            <div class="mb-3">
                <h6>查询结果 (${result.data.length} 条记录):</h6>
                <div class="table-responsive">
                    <table class="table table-striped table-hover sql-result-table">
                        <thead>
                            <tr>
        `;

        // 添加表头
        const firstRow = result.data[0];
        for (const key in firstRow) {
            html += `<th>${escapeHtml(key)}</th>`;
        }
        html += `</tr></thead><tbody>`;

        // 添加数据行（最多显示100行）
        const maxRows = Math.min(100, result.data.length);
        for (let i = 0; i < maxRows; i++) {
            html += '<tr>';
            for (const key in firstRow) {
                const value = result.data[i][key];
                html += `<td>${value !== null ? escapeHtml(String(value)) : '<em>null</em>'}</td>`;
            }
            html += '</tr>';
        }

        html += '</tbody></table>';

        if (result.data.length > maxRows) {
            html += `<p class="text-muted">... 还有 ${result.data.length - maxRows} 条记录未显示</p>`;
        }

        html += '</div></div>';
    } else {
        html += '<div class="alert alert-info">查询结果为空</div>';
    }

    content.innerHTML = html;
    modal.show();
}

// 加载数据库信息
async function loadDatabaseInfo() {
    try {
        const response = await fetch('/api/database/summary');
        const summary = await response.json();

        let html = '';
        if (summary.error) {
            html = `<div class="text-danger">${summary.error}</div>`;
        } else {
            html = `
                <div><strong>表数量:</strong> ${summary.tableCount}</div>
                <div class="mt-2"><strong>表列表:</strong></div>
                <ul class="list-unstyled ms-2">
            `;

            for (const tableName of summary.tableNames) {
                const rowCount = summary.tableRowCounts[tableName] || 0;
                html += `<li>• ${tableName} (${rowCount} 条记录)</li>`;
            }

            html += '</ul>';
        }

        document.getElementById('dbSummary').innerHTML = html;
    } catch (error) {
        console.error('加载数据库信息失败:', error);
        document.getElementById('dbSummary').innerHTML =
            '<div class="text-danger">加载失败</div>';
    }
}

// 加载聊天历史
async function loadChatHistory() {
    try {
        const response = await fetch(`/api/chat/history/${currentSessionId}`);
        const history = await response.json();

        const chatMessages = document.getElementById('chatMessages');
        chatMessages.innerHTML = '';

        if (history.length === 0) {
            chatMessages.innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-robot fa-3x mb-3"></i>
                    <p>欢迎使用ChatCharge！选择模型并开始对话吧。</p>
                </div>
            `;
            messageCount = 0;
        } else {
            for (const message of history) {
                addMessageToChat(
                    message.role.toLowerCase(),
                    message.content,
                    message.thinkingContent,
                    message.modelName
                );
            }
        }

        document.getElementById('messageCount').textContent = messageCount;
    } catch (error) {
        console.error('加载聊天历史失败:', error);
    }
}

// 加载历史记录列表
async function loadHistoryList() {
    try {
        const response = await fetch('/api/chat/sessions');
        const sessions = await response.json();

        const historySelect = document.getElementById('historySelect');
        historySelect.innerHTML = '<option value="">选择历史会话</option>';

        for (const sessionId of sessions) {
            const option = document.createElement('option');
            option.value = sessionId;
            option.textContent = sessionId === 'default' ? '默认会话' : sessionId;
            historySelect.appendChild(option);
        }
    } catch (error) {
        console.error('加载历史记录列表失败:', error);
    }
}

// 加载特定历史记录
function loadSpecificHistory(sessionId) {
    currentSessionId = sessionId;
    loadChatHistory();
}

// 清除当前历史记录
async function clearCurrentHistory() {
    if (!confirm('确定要清除当前会话的历史记录吗？')) {
        return;
    }

    try {
        await fetch(`/api/chat/history/${currentSessionId}`, {
            method: 'DELETE'
        });

        // 重新加载聊天历史
        loadChatHistory();
        loadHistoryList();

        alert('历史记录已清除');
    } catch (error) {
        console.error('清除历史记录失败:', error);
        alert('清除失败: ' + error.message);
    }
}

// 导出历史记录
async function exportHistory() {
    try {
        const response = await fetch(`/api/chat/export/${currentSessionId}`);
        const blob = await response.blob();

        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `chat_history_${currentSessionId}_${new Date().toISOString().slice(0, 10)}.csv`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);

        alert('历史记录已导出');
    } catch (error) {
        console.error('导出历史记录失败:', error);
        alert('导出失败: ' + error.message);
    }
}

// 导入Excel文件
async function importExcel() {
    const fileInput = document.getElementById('excelFile');
    const file = fileInput.files[0];

    if (!file) {
        alert('请选择Excel文件');
        return;
    }

    if (!file.name.toLowerCase().match(/\.(xlsx|xls)$/)) {
        alert('请选择有效的Excel文件 (.xlsx 或 .xls)');
        return;
    }

    const tableName = prompt('请输入表名:', file.name.replace(/\.(xlsx|xls)$/i, ''));
    if (!tableName) {
        return;
    }

    const formData = new FormData();
    formData.append('file', file);
    formData.append('tableName', tableName);

    try {
        // 显示加载提示
        const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
        loadingModal.show();

        const response = await fetch('/api/excel/import', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        loadingModal.hide();

        if (response.ok) {
            alert(`Excel文件导入成功！\n表名: ${result.tableName}\n导入记录数: ${result.recordCount}`);

            // 清空文件选择
            fileInput.value = '';

            // 如果当前是SQL模式，刷新数据库信息
            const mode = document.querySelector('input[name="chatMode"]:checked').value;
            if (mode === 'sql') {
                loadDatabaseInfo();
            }
        } else {
            alert('导入失败: ' + (result.error || '未知错误'));
        }
    } catch (error) {
        console.error('Excel导入失败:', error);
        alert('导入失败: ' + error.message);

        // 隐藏加载提示
        const loadingModal = bootstrap.Modal.getInstance(document.getElementById('loadingModal'));
        if (loadingModal) {
            loadingModal.hide();
        }
    }
}

// 移动端菜单切换
function toggleMobileMenu() {
    const sidebar = document.querySelector('.col-md-3');
    sidebar.classList.toggle('show');
}

// 响应式处理
function handleResize() {
    const sidebar = document.querySelector('.col-md-3');
    if (window.innerWidth > 768) {
        sidebar.classList.remove('show');
    }
}

// 添加移动端支持
window.addEventListener('resize', handleResize);

// 点击外部区域关闭移动端菜单
document.addEventListener('click', function(e) {
    const sidebar = document.querySelector('.col-md-3');
    const toggleButton = document.querySelector('.mobile-menu-toggle');

    if (window.innerWidth <= 768 &&
        sidebar.classList.contains('show') &&
        !sidebar.contains(e.target) &&
        !toggleButton?.contains(e.target)) {
        sidebar.classList.remove('show');
    }
});

// 工具函数：格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 工具函数：格式化时间
function formatTime(timestamp) {
    const date = new Date(timestamp);
    return date.toLocaleString('zh-CN');
}

// 添加键盘快捷键支持
document.addEventListener('keydown', function(e) {
    // Ctrl + / 显示快捷键帮助
    if (e.ctrlKey && e.key === '/') {
        e.preventDefault();
        showShortcutHelp();
    }

    // Esc 关闭模态框
    if (e.key === 'Escape') {
        const modals = document.querySelectorAll('.modal.show');
        modals.forEach(modal => {
            const modalInstance = bootstrap.Modal.getInstance(modal);
            if (modalInstance) {
                modalInstance.hide();
            }
        });
    }
});

// 显示快捷键帮助
function showShortcutHelp() {
    alert(`快捷键帮助：

Ctrl + Enter: 发送消息
Ctrl + /: 显示此帮助
Esc: 关闭模态框

提示：
- 在数据库查询模式下，可以用自然语言描述您想要查询的数据
- 支持导入Excel文件到数据库进行查询分析
- 聊天历史会自动保存，最多保留10轮对话`);
}
