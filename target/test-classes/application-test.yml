server:
  port: 0

spring:
  application:
    name: ChatCharge-Test
  
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
    
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    
  h2:
    console:
      enabled: false

logging:
  level:
    com.chatcharge: INFO
    org.springframework.web: WARN
    
app:
  chat:
    history:
      max-records: 5
      csv-file: ./test-data/chat_history.csv
    security:
      enable-html-sanitization: true
      max-message-length: 1000
  database:
    data-directory: ./test-data/
    excel-import-directory: ./test-data/excel/

llm:
  models:
    - name: "Test-Model"
      displayName: "Test Model"
      apiUrl: "http://localhost:8080/test"
      apiKey: "test-key"
      defaultParams:
        temperature: 0.5
        maxTokens: 1000
        topP: 0.8
      supportThinking: true

  defaultModel: "Test-Model"
  requestTimeout: 5000
  maxRetries: 1

system-prompts:
  database-assistant: "Test database assistant prompt"
  general-chat: "Test general chat prompt"

prompt-templates:
  sql-query-template: "Test SQL query template: {user_question}"
  data-analysis-template: "Test data analysis template: {query_results}"

default:
  system-prompt: "general-chat"
  max-prompt-length: 1000
  enable-context-injection: true
