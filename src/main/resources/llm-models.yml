llm:
  models:
    - name: "DeepSeek-V3"
      displayName: "DeepSeek V3"
      apiUrl: "https://api.deepseek.com/v1/chat/completions"
      apiKey: "${DEEPSEEK_API_KEY:your-deepseek-api-key}"
      defaultParams:
        temperature: 0.7
        maxTokens: 4000
        topP: 0.9
        frequencyPenalty: 0.0
        presencePenalty: 0.0
      supportThinking: true
      
    - name: "Qwen3-Turbo"
      displayName: "Qwen 3 Turbo"
      apiUrl: "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
      apiKey: "${QWEN_API_KEY:your-qwen-api-key}"
      defaultParams:
        temperature: 0.7
        maxTokens: 4000
        topP: 0.9
        frequencyPenalty: 0.0
        presencePenalty: 0.0
      supportThinking: true
      
    - name: "GPT-4"
      displayName: "GPT-4"
      apiUrl: "https://api.openai.com/v1/chat/completions"
      apiKey: "${OPENAI_API_KEY:your-openai-api-key}"
      defaultParams:
        temperature: 0.7
        maxTokens: 4000
        topP: 0.9
        frequencyPenalty: 0.0
        presencePenalty: 0.0
      supportThinking: false
      
    - name: "Claude-3.5-Sonnet"
      displayName: "Claude 3.5 Sonnet"
      apiUrl: "https://api.anthropic.com/v1/messages"
      apiKey: "${CLAUDE_API_KEY:your-claude-api-key}"
      defaultParams:
        temperature: 0.7
        maxTokens: 4000
        topP: 0.9
        frequencyPenalty: 0.0
        presencePenalty: 0.0
      supportThinking: false

  defaultModel: "DeepSeek-V3"
  
  requestTimeout: 60000
  maxRetries: 3
