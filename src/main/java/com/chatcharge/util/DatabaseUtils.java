package com.chatcharge.util;

import com.chatcharge.config.AppConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 数据库工具类
 */
@Component
public class DatabaseUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(DatabaseUtils.class);
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    @Autowired
    private DataSource dataSource;
    
    @Autowired
    private AppConfig appConfig;
    
    @Autowired
    private ExcelUtils excelUtils;
    
    /**
     * 表结构信息
     */
    public static class TableInfo {
        private String tableName;
        private List<ColumnInfo> columns;
        private List<String> primaryKeys;
        
        public TableInfo(String tableName) {
            this.tableName = tableName;
            this.columns = new ArrayList<>();
            this.primaryKeys = new ArrayList<>();
        }
        
        // Getters and Setters
        public String getTableName() {
            return tableName;
        }
        
        public void setTableName(String tableName) {
            this.tableName = tableName;
        }
        
        public List<ColumnInfo> getColumns() {
            return columns;
        }
        
        public void setColumns(List<ColumnInfo> columns) {
            this.columns = columns;
        }
        
        public List<String> getPrimaryKeys() {
            return primaryKeys;
        }
        
        public void setPrimaryKeys(List<String> primaryKeys) {
            this.primaryKeys = primaryKeys;
        }
        
        public void addColumn(ColumnInfo column) {
            this.columns.add(column);
        }
    }
    
    /**
     * 列信息
     */
    public static class ColumnInfo {
        private String columnName;
        private String dataType;
        private int columnSize;
        private boolean nullable;
        private String defaultValue;
        private boolean isPrimaryKey;
        
        // Getters and Setters
        public String getColumnName() {
            return columnName;
        }
        
        public void setColumnName(String columnName) {
            this.columnName = columnName;
        }
        
        public String getDataType() {
            return dataType;
        }
        
        public void setDataType(String dataType) {
            this.dataType = dataType;
        }
        
        public int getColumnSize() {
            return columnSize;
        }
        
        public void setColumnSize(int columnSize) {
            this.columnSize = columnSize;
        }
        
        public boolean isNullable() {
            return nullable;
        }
        
        public void setNullable(boolean nullable) {
            this.nullable = nullable;
        }
        
        public String getDefaultValue() {
            return defaultValue;
        }
        
        public void setDefaultValue(String defaultValue) {
            this.defaultValue = defaultValue;
        }
        
        public boolean isPrimaryKey() {
            return isPrimaryKey;
        }
        
        public void setPrimaryKey(boolean primaryKey) {
            isPrimaryKey = primaryKey;
        }
    }
    
    /**
     * 将Excel数据导入到数据库表
     * @param excelFilePath Excel文件路径
     * @param tableName 目标表名
     * @param sheetIndex 工作表索引（可选）
     * @return 导入的记录数
     */
    public int importExcelToTable(String excelFilePath, String tableName, Integer sheetIndex) throws Exception {
        ExcelUtils.ExcelData excelData;
        
        if (sheetIndex != null) {
            excelData = excelUtils.readExcelSheet(excelFilePath, sheetIndex);
        } else {
            List<ExcelUtils.ExcelData> dataList = excelUtils.readExcelFile(excelFilePath);
            if (dataList.isEmpty()) {
                throw new IllegalArgumentException("Excel file is empty");
            }
            excelData = dataList.get(0); // 使用第一个工作表
        }
        
        // 创建表（如果不存在）
        createTableFromExcelData(tableName, excelData);
        
        // 插入数据
        return insertExcelDataToTable(tableName, excelData);
    }
    
    /**
     * 根据Excel数据创建表
     */
    private void createTableFromExcelData(String tableName, ExcelUtils.ExcelData excelData) throws SQLException {
        StringBuilder ddl = new StringBuilder();
        ddl.append("CREATE TABLE IF NOT EXISTS ").append(tableName).append(" (");
        
        List<String> headers = excelData.getHeaders();
        for (int i = 0; i < headers.size(); i++) {
            String columnName = sanitizeColumnName(headers.get(i));
            String dataType = inferDataType(excelData, i);
            
            ddl.append(columnName).append(" ").append(dataType);
            
            if (i < headers.size() - 1) {
                ddl.append(", ");
            }
        }
        
        ddl.append(")");
        
        try (Connection conn = dataSource.getConnection();
             Statement stmt = conn.createStatement()) {
            stmt.execute(ddl.toString());
            logger.info("Created table: {}", tableName);
        }
    }
    
    /**
     * 将Excel数据插入到表中
     */
    private int insertExcelDataToTable(String tableName, ExcelUtils.ExcelData excelData) throws SQLException {
        List<String> headers = excelData.getHeaders();
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO ").append(tableName).append(" (");
        
        // 构建列名
        for (int i = 0; i < headers.size(); i++) {
            sql.append(sanitizeColumnName(headers.get(i)));
            if (i < headers.size() - 1) {
                sql.append(", ");
            }
        }
        
        sql.append(") VALUES (");
        
        // 构建占位符
        for (int i = 0; i < headers.size(); i++) {
            sql.append("?");
            if (i < headers.size() - 1) {
                sql.append(", ");
            }
        }
        
        sql.append(")");
        
        int insertedCount = 0;
        try (Connection conn = dataSource.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql.toString())) {
            
            for (List<Object> row : excelData.getRows()) {
                for (int i = 0; i < row.size(); i++) {
                    Object value = row.get(i);
                    if (value instanceof Date) {
                        pstmt.setTimestamp(i + 1, new Timestamp(((Date) value).getTime()));
                    } else {
                        pstmt.setObject(i + 1, value);
                    }
                }
                
                pstmt.addBatch();
                insertedCount++;
                
                // 每1000条记录执行一次批处理
                if (insertedCount % 1000 == 0) {
                    pstmt.executeBatch();
                }
            }
            
            // 执行剩余的批处理
            pstmt.executeBatch();
        }
        
        logger.info("Inserted {} records into table: {}", insertedCount, tableName);
        return insertedCount;
    }
    
    /**
     * 清理列名，确保符合SQL标准
     */
    private String sanitizeColumnName(String columnName) {
        if (columnName == null || columnName.trim().isEmpty()) {
            return "COLUMN_" + System.currentTimeMillis();
        }
        
        // 移除特殊字符，只保留字母、数字和下划线
        String sanitized = columnName.replaceAll("[^a-zA-Z0-9_\\u4e00-\\u9fa5]", "_");
        
        // 确保以字母开头
        if (!sanitized.matches("^[a-zA-Z_\\u4e00-\\u9fa5].*")) {
            sanitized = "COL_" + sanitized;
        }
        
        return sanitized.toUpperCase();
    }
    
    /**
     * 推断数据类型
     */
    private String inferDataType(ExcelUtils.ExcelData excelData, int columnIndex) {
        List<List<Object>> rows = excelData.getRows();
        
        // 检查前100行数据来推断类型
        int checkRows = Math.min(100, rows.size());
        Map<Class<?>, Integer> typeCount = new HashMap<>();
        
        for (int i = 0; i < checkRows; i++) {
            if (columnIndex < rows.get(i).size()) {
                Object value = rows.get(i).get(columnIndex);
                if (value != null) {
                    Class<?> valueClass = value.getClass();
                    typeCount.put(valueClass, typeCount.getOrDefault(valueClass, 0) + 1);
                }
            }
        }
        
        // 根据最常见的类型确定数据类型
        Class<?> mostCommonType = typeCount.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse(String.class);
        
        if (mostCommonType == Long.class || mostCommonType == Integer.class) {
            return "BIGINT";
        } else if (mostCommonType == Double.class || mostCommonType == Float.class) {
            return "DOUBLE";
        } else if (mostCommonType == Boolean.class) {
            return "BOOLEAN";
        } else if (mostCommonType == Date.class) {
            return "TIMESTAMP";
        } else {
            return "VARCHAR(1000)";
        }
    }
    
    /**
     * 获取表结构信息
     */
    public TableInfo getTableStructure(String tableName) throws SQLException {
        TableInfo tableInfo = new TableInfo(tableName);
        
        try (Connection conn = dataSource.getConnection()) {
            DatabaseMetaData metaData = conn.getMetaData();
            
            // 获取列信息
            try (ResultSet columns = metaData.getColumns(null, null, tableName.toUpperCase(), null)) {
                while (columns.next()) {
                    ColumnInfo columnInfo = new ColumnInfo();
                    columnInfo.setColumnName(columns.getString("COLUMN_NAME"));
                    columnInfo.setDataType(columns.getString("TYPE_NAME"));
                    columnInfo.setColumnSize(columns.getInt("COLUMN_SIZE"));
                    columnInfo.setNullable(columns.getInt("NULLABLE") == DatabaseMetaData.columnNullable);
                    columnInfo.setDefaultValue(columns.getString("COLUMN_DEF"));
                    
                    tableInfo.addColumn(columnInfo);
                }
            }
            
            // 获取主键信息
            try (ResultSet primaryKeys = metaData.getPrimaryKeys(null, null, tableName.toUpperCase())) {
                while (primaryKeys.next()) {
                    String pkColumnName = primaryKeys.getString("COLUMN_NAME");
                    tableInfo.getPrimaryKeys().add(pkColumnName);
                    
                    // 标记主键列
                    tableInfo.getColumns().stream()
                            .filter(col -> col.getColumnName().equals(pkColumnName))
                            .forEach(col -> col.setPrimaryKey(true));
                }
            }
        }
        
        return tableInfo;
    }
    
    /**
     * 生成表的DDL语句
     */
    public String generateTableDDL(String tableName) throws SQLException {
        TableInfo tableInfo = getTableStructure(tableName);
        StringBuilder ddl = new StringBuilder();
        
        ddl.append("CREATE TABLE ").append(tableName).append(" (\n");
        
        List<ColumnInfo> columns = tableInfo.getColumns();
        for (int i = 0; i < columns.size(); i++) {
            ColumnInfo column = columns.get(i);
            ddl.append("  ").append(column.getColumnName())
               .append(" ").append(column.getDataType());
            
            if (column.getColumnSize() > 0 && 
                (column.getDataType().contains("VARCHAR") || column.getDataType().contains("CHAR"))) {
                ddl.append("(").append(column.getColumnSize()).append(")");
            }
            
            if (!column.isNullable()) {
                ddl.append(" NOT NULL");
            }
            
            if (column.getDefaultValue() != null) {
                ddl.append(" DEFAULT ").append(column.getDefaultValue());
            }
            
            if (i < columns.size() - 1) {
                ddl.append(",");
            }
            ddl.append("\n");
        }
        
        // 添加主键约束
        if (!tableInfo.getPrimaryKeys().isEmpty()) {
            ddl.append(",\n  PRIMARY KEY (");
            ddl.append(String.join(", ", tableInfo.getPrimaryKeys()));
            ddl.append(")\n");
        }
        
        ddl.append(");");
        
        return ddl.toString();
    }
    
    /**
     * 获取所有表名
     */
    public List<String> getAllTableNames() throws SQLException {
        List<String> tableNames = new ArrayList<>();
        
        try (Connection conn = dataSource.getConnection()) {
            DatabaseMetaData metaData = conn.getMetaData();
            try (ResultSet tables = metaData.getTables(null, null, null, new String[]{"TABLE"})) {
                while (tables.next()) {
                    String tableName = tables.getString("TABLE_NAME");
                    if (!tableName.startsWith("INFORMATION_SCHEMA") && !tableName.startsWith("SYS")) {
                        tableNames.add(tableName);
                    }
                }
            }
        }
        
        return tableNames;
    }
    
    /**
     * 执行查询并返回结果
     */
    public List<Map<String, Object>> executeQuery(String sql) throws SQLException {
        List<Map<String, Object>> results = new ArrayList<>();
        
        try (Connection conn = dataSource.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            
            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();
            
            while (rs.next()) {
                Map<String, Object> row = new LinkedHashMap<>();
                for (int i = 1; i <= columnCount; i++) {
                    String columnName = metaData.getColumnLabel(i);
                    Object value = rs.getObject(i);
                    row.put(columnName, value);
                }
                results.add(row);
            }
        }
        
        return results;
    }
}
