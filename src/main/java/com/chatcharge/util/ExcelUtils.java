package com.chatcharge.util;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.FileInputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * Excel文件处理工具类
 */
@Component
public class ExcelUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(ExcelUtils.class);
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * Excel数据结构
     */
    public static class ExcelData {
        private List<String> headers;
        private List<List<Object>> rows;
        private String sheetName;
        
        public ExcelData() {
            this.headers = new ArrayList<>();
            this.rows = new ArrayList<>();
        }
        
        public ExcelData(String sheetName) {
            this();
            this.sheetName = sheetName;
        }
        
        // Getters and Setters
        public List<String> getHeaders() {
            return headers;
        }
        
        public void setHeaders(List<String> headers) {
            this.headers = headers;
        }
        
        public List<List<Object>> getRows() {
            return rows;
        }
        
        public void setRows(List<List<Object>> rows) {
            this.rows = rows;
        }
        
        public String getSheetName() {
            return sheetName;
        }
        
        public void setSheetName(String sheetName) {
            this.sheetName = sheetName;
        }
        
        public void addRow(List<Object> row) {
            this.rows.add(row);
        }
        
        public int getRowCount() {
            return rows.size();
        }
        
        public int getColumnCount() {
            return headers.size();
        }
    }
    
    /**
     * 读取Excel文件
     * @param filePath Excel文件路径
     * @return Excel数据列表（支持多个工作表）
     */
    public List<ExcelData> readExcelFile(String filePath) throws IOException {
        List<ExcelData> excelDataList = new ArrayList<>();
        
        try (FileInputStream fis = new FileInputStream(filePath)) {
            Workbook workbook = createWorkbook(filePath, fis);
            
            // 遍历所有工作表
            for (int sheetIndex = 0; sheetIndex < workbook.getNumberOfSheets(); sheetIndex++) {
                Sheet sheet = workbook.getSheetAt(sheetIndex);
                ExcelData excelData = readSheet(sheet);
                excelDataList.add(excelData);
            }
            
            workbook.close();
        }
        
        return excelDataList;
    }
    
    /**
     * 读取指定工作表
     * @param filePath Excel文件路径
     * @param sheetName 工作表名称
     * @return Excel数据
     */
    public ExcelData readExcelSheet(String filePath, String sheetName) throws IOException {
        try (FileInputStream fis = new FileInputStream(filePath)) {
            Workbook workbook = createWorkbook(filePath, fis);
            Sheet sheet = workbook.getSheet(sheetName);
            
            if (sheet == null) {
                throw new IllegalArgumentException("Sheet not found: " + sheetName);
            }
            
            ExcelData excelData = readSheet(sheet);
            workbook.close();
            
            return excelData;
        }
    }
    
    /**
     * 读取指定工作表（按索引）
     * @param filePath Excel文件路径
     * @param sheetIndex 工作表索引
     * @return Excel数据
     */
    public ExcelData readExcelSheet(String filePath, int sheetIndex) throws IOException {
        try (FileInputStream fis = new FileInputStream(filePath)) {
            Workbook workbook = createWorkbook(filePath, fis);
            
            if (sheetIndex >= workbook.getNumberOfSheets()) {
                throw new IllegalArgumentException("Sheet index out of range: " + sheetIndex);
            }
            
            Sheet sheet = workbook.getSheetAt(sheetIndex);
            ExcelData excelData = readSheet(sheet);
            workbook.close();
            
            return excelData;
        }
    }
    
    /**
     * 创建工作簿对象
     */
    private Workbook createWorkbook(String filePath, FileInputStream fis) throws IOException {
        if (filePath.toLowerCase().endsWith(".xlsx")) {
            return new XSSFWorkbook(fis);
        } else if (filePath.toLowerCase().endsWith(".xls")) {
            return new HSSFWorkbook(fis);
        } else {
            throw new IllegalArgumentException("Unsupported file format. Only .xls and .xlsx are supported.");
        }
    }
    
    /**
     * 读取工作表数据
     */
    private ExcelData readSheet(Sheet sheet) {
        ExcelData excelData = new ExcelData(sheet.getSheetName());
        
        Iterator<Row> rowIterator = sheet.iterator();
        boolean isFirstRow = true;
        
        while (rowIterator.hasNext()) {
            Row row = rowIterator.next();
            
            if (isFirstRow) {
                // 读取标题行
                List<String> headers = readHeaderRow(row);
                excelData.setHeaders(headers);
                isFirstRow = false;
            } else {
                // 读取数据行
                List<Object> rowData = readDataRow(row, excelData.getColumnCount());
                if (!isEmptyRow(rowData)) {
                    excelData.addRow(rowData);
                }
            }
        }
        
        return excelData;
    }
    
    /**
     * 读取标题行
     */
    private List<String> readHeaderRow(Row row) {
        List<String> headers = new ArrayList<>();
        
        for (Cell cell : row) {
            String headerValue = getCellValueAsString(cell);
            headers.add(headerValue != null ? headerValue.trim() : "");
        }
        
        return headers;
    }
    
    /**
     * 读取数据行
     */
    private List<Object> readDataRow(Row row, int expectedColumnCount) {
        List<Object> rowData = new ArrayList<>();
        
        for (int i = 0; i < expectedColumnCount; i++) {
            Cell cell = row.getCell(i);
            Object cellValue = getCellValue(cell);
            rowData.add(cellValue);
        }
        
        return rowData;
    }
    
    /**
     * 获取单元格值
     */
    private Object getCellValue(Cell cell) {
        if (cell == null) {
            return null;
        }
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue();
                } else {
                    double numericValue = cell.getNumericCellValue();
                    // 如果是整数，返回Long，否则返回Double
                    if (numericValue == Math.floor(numericValue)) {
                        return (long) numericValue;
                    } else {
                        return numericValue;
                    }
                }
            case BOOLEAN:
                return cell.getBooleanCellValue();
            case FORMULA:
                return evaluateFormula(cell);
            case BLANK:
                return null;
            default:
                return cell.toString();
        }
    }
    
    /**
     * 获取单元格值作为字符串
     */
    private String getCellValueAsString(Cell cell) {
        Object value = getCellValue(cell);
        return value != null ? value.toString() : "";
    }
    
    /**
     * 计算公式单元格的值
     */
    private Object evaluateFormula(Cell cell) {
        try {
            FormulaEvaluator evaluator = cell.getSheet().getWorkbook().getCreationHelper().createFormulaEvaluator();
            CellValue cellValue = evaluator.evaluate(cell);
            
            switch (cellValue.getCellType()) {
                case STRING:
                    return cellValue.getStringValue();
                case NUMERIC:
                    double numericValue = cellValue.getNumberValue();
                    if (numericValue == Math.floor(numericValue)) {
                        return (long) numericValue;
                    } else {
                        return numericValue;
                    }
                case BOOLEAN:
                    return cellValue.getBooleanValue();
                default:
                    return cell.getCellFormula();
            }
        } catch (Exception e) {
            logger.warn("Failed to evaluate formula in cell: " + cell.getAddress(), e);
            return cell.getCellFormula();
        }
    }
    
    /**
     * 检查行是否为空
     */
    private boolean isEmptyRow(List<Object> rowData) {
        return rowData.stream().allMatch(Objects::isNull);
    }
    
    /**
     * 获取Excel文件的工作表名称列表
     */
    public List<String> getSheetNames(String filePath) throws IOException {
        List<String> sheetNames = new ArrayList<>();
        
        try (FileInputStream fis = new FileInputStream(filePath)) {
            Workbook workbook = createWorkbook(filePath, fis);
            
            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                sheetNames.add(workbook.getSheetName(i));
            }
            
            workbook.close();
        }
        
        return sheetNames;
    }
    
    /**
     * 验证Excel文件格式
     */
    public boolean isValidExcelFile(String filePath) {
        return filePath != null && 
               (filePath.toLowerCase().endsWith(".xls") || filePath.toLowerCase().endsWith(".xlsx"));
    }
    
    /**
     * 获取Excel文件信息
     */
    public Map<String, Object> getExcelFileInfo(String filePath) throws IOException {
        Map<String, Object> info = new HashMap<>();
        
        try (FileInputStream fis = new FileInputStream(filePath)) {
            Workbook workbook = createWorkbook(filePath, fis);
            
            info.put("fileName", filePath);
            info.put("sheetCount", workbook.getNumberOfSheets());
            info.put("sheetNames", getSheetNames(filePath));
            info.put("readTime", LocalDateTime.now().format(DATE_FORMATTER));
            
            // 获取每个工作表的行数和列数
            List<Map<String, Object>> sheetInfos = new ArrayList<>();
            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                Sheet sheet = workbook.getSheetAt(i);
                Map<String, Object> sheetInfo = new HashMap<>();
                sheetInfo.put("name", sheet.getSheetName());
                sheetInfo.put("rowCount", sheet.getLastRowNum() + 1);
                
                if (sheet.getLastRowNum() >= 0) {
                    Row firstRow = sheet.getRow(0);
                    sheetInfo.put("columnCount", firstRow != null ? firstRow.getLastCellNum() : 0);
                } else {
                    sheetInfo.put("columnCount", 0);
                }
                
                sheetInfos.add(sheetInfo);
            }
            info.put("sheets", sheetInfos);
            
            workbook.close();
        }
        
        return info;
    }
}
