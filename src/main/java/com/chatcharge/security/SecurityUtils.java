package com.chatcharge.security;

import org.apache.commons.text.StringEscapeUtils;
import org.jsoup.Jsoup;
import org.jsoup.safety.Safelist;
import org.springframework.stereotype.Component;

import java.util.regex.Pattern;

/**
 * 安全工具类，处理各种安全相关的操作
 */
@Component
public class SecurityUtils {
    
    // 危险HTML标签和属性的正则表达式
    private static final Pattern SCRIPT_PATTERN = Pattern.compile(
        "<script[^>]*>.*?</script>", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
    private static final Pattern JAVASCRIPT_PATTERN = Pattern.compile(
        "javascript:", Pattern.CASE_INSENSITIVE);
    private static final Pattern ON_EVENT_PATTERN = Pattern.compile(
        "\\s*on\\w+\\s*=", Pattern.CASE_INSENSITIVE);
    
    // 允许的HTML标签白名单（用于思考内容的格式化）
    private static final Safelist THINKING_SAFELIST = Safelist.basic()
        .addTags("div", "span", "br", "p")
        .addAttributes("div", "class", "style")
        .addAttributes("span", "class", "style");
    
    /**
     * 清理用户输入，防止XSS攻击
     * @param input 用户输入的内容
     * @return 清理后的安全内容
     */
    public String sanitizeUserInput(String input) {
        if (input == null || input.trim().isEmpty()) {
            return "";
        }
        
        // 1. HTML转义
        String escaped = StringEscapeUtils.escapeHtml4(input);
        
        // 2. 移除潜在的危险内容
        escaped = removeDangerousContent(escaped);
        
        // 3. 限制长度
        if (escaped.length() > 10000) {
            escaped = escaped.substring(0, 10000) + "...";
        }
        
        return escaped;
    }
    
    /**
     * 清理LLM返回的内容，保留必要的格式但移除危险代码
     * @param content LLM返回的内容
     * @param isThinkingContent 是否为思考内容
     * @return 清理后的内容
     */
    public String sanitizeLlmResponse(String content, boolean isThinkingContent) {
        if (content == null || content.trim().isEmpty()) {
            return "";
        }
        
        // 对于思考内容，允许基本的HTML格式化
        if (isThinkingContent) {
            return Jsoup.clean(content, THINKING_SAFELIST);
        }
        
        // 对于普通内容，进行严格的清理
        String cleaned = removeDangerousContent(content);
        
        // 保留换行符但转义其他HTML
        cleaned = StringEscapeUtils.escapeHtml4(cleaned);
        cleaned = cleaned.replace("&lt;br&gt;", "<br>")
                        .replace("&lt;/br&gt;", "</br>")
                        .replace("\n", "<br>");
        
        return cleaned;
    }
    
    /**
     * 移除危险的HTML内容
     * @param content 原始内容
     * @return 清理后的内容
     */
    private String removeDangerousContent(String content) {
        // 移除script标签
        content = SCRIPT_PATTERN.matcher(content).replaceAll("");
        
        // 移除javascript:协议
        content = JAVASCRIPT_PATTERN.matcher(content).replaceAll("");
        
        // 移除事件处理器
        content = ON_EVENT_PATTERN.matcher(content).replaceAll(" ");
        
        // 移除其他潜在危险的标签
        content = content.replaceAll("(?i)<\\s*(iframe|object|embed|form|input|textarea|select|button)[^>]*>", "");
        content = content.replaceAll("(?i)</\\s*(iframe|object|embed|form|input|textarea|select|button)\\s*>", "");
        
        return content;
    }
    
    /**
     * 验证输入长度
     * @param input 输入内容
     * @param maxLength 最大长度
     * @return 是否有效
     */
    public boolean validateInputLength(String input, int maxLength) {
        return input != null && input.length() <= maxLength;
    }
    
    /**
     * 清理SQL查询中的危险内容（基础防护）
     * @param sql SQL查询
     * @return 清理后的SQL
     */
    public String sanitizeSqlQuery(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return "";
        }
        
        // 移除潜在的SQL注入关键词
        String[] dangerousKeywords = {
            "DROP", "DELETE", "INSERT", "UPDATE", "ALTER", "CREATE", 
            "TRUNCATE", "EXEC", "EXECUTE", "UNION", "SCRIPT", "--", "/*", "*/"
        };
        
        String cleanSql = sql;
        for (String keyword : dangerousKeywords) {
            cleanSql = cleanSql.replaceAll("(?i)\\b" + keyword + "\\b", "");
        }
        
        return cleanSql.trim();
    }
    
    /**
     * 转义CSV字段中的特殊字符
     * @param field CSV字段内容
     * @return 转义后的内容
     */
    public String escapeCsvField(String field) {
        if (field == null) {
            return "";
        }
        
        // 如果包含逗号、引号或换行符，需要用引号包围并转义内部引号
        if (field.contains(",") || field.contains("\"") || field.contains("\n") || field.contains("\r")) {
            return "\"" + field.replace("\"", "\"\"") + "\"";
        }
        
        return field;
    }
    
    /**
     * 检测是否为思考内容（针对DeepSeek和Qwen等模型）
     * @param content 内容
     * @return 是否为思考内容
     */
    public boolean isThinkingContent(String content) {
        if (content == null || content.trim().isEmpty()) {
            return false;
        }
        
        // 检测常见的思考标记
        String lowerContent = content.toLowerCase();
        return lowerContent.contains("<thinking>") || 
               lowerContent.contains("思考：") ||
               lowerContent.contains("分析：") ||
               lowerContent.contains("reasoning:") ||
               lowerContent.contains("analysis:");
    }
    
    /**
     * 提取思考内容
     * @param content 完整内容
     * @return 思考部分的内容
     */
    public String extractThinkingContent(String content) {
        if (content == null) {
            return "";
        }
        
        // 提取<thinking>标签内的内容
        Pattern thinkingPattern = Pattern.compile(
            "<thinking>(.*?)</thinking>", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
        java.util.regex.Matcher matcher = thinkingPattern.matcher(content);
        
        if (matcher.find()) {
            return matcher.group(1).trim();
        }
        
        return "";
    }
    
    /**
     * 移除思考内容，返回最终回复
     * @param content 完整内容
     * @return 移除思考后的内容
     */
    public String removeThinkingContent(String content) {
        if (content == null) {
            return "";
        }
        
        // 移除<thinking>标签及其内容
        return content.replaceAll("(?i)<thinking>.*?</thinking>", "").trim();
    }
}
