package com.chatcharge.controller;

import com.chatcharge.service.LlmService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * Web页面控制器
 */
@Controller
public class WebController {
    
    @Autowired
    private LlmService llmService;
    
    /**
     * 主页
     */
    @GetMapping("/")
    public String index(Model model) {
        // 添加模型列表到模型中
        model.addAttribute("models", llmService.getAvailableModels());
        model.addAttribute("defaultModel", llmService.getDefaultModel());
        
        return "index";
    }
    
    /**
     * 聊天页面（重定向到主页）
     */
    @GetMapping("/chat")
    public String chat() {
        return "redirect:/";
    }
}
