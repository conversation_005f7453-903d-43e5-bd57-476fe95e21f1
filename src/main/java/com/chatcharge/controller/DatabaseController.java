package com.chatcharge.controller;

import com.chatcharge.service.NaturalLanguageQueryService;
import com.chatcharge.util.DatabaseUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据库操作控制器
 */
@RestController
@RequestMapping("/api/database")
public class DatabaseController {
    
    private static final Logger logger = LoggerFactory.getLogger(DatabaseController.class);
    
    @Autowired
    private DatabaseUtils databaseUtils;
    
    @Autowired
    private NaturalLanguageQueryService queryService;
    
    /**
     * 获取数据库摘要信息
     */
    @GetMapping("/summary")
    public ResponseEntity<Map<String, Object>> getDatabaseSummary() {
        try {
            Map<String, Object> summary = queryService.getDatabaseSummary();
            return ResponseEntity.ok(summary);
        } catch (Exception e) {
            logger.error("Failed to get database summary", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", "获取数据库信息失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResult);
        }
    }
    
    /**
     * 获取所有表名
     */
    @GetMapping("/tables")
    public ResponseEntity<List<String>> getAllTables() {
        try {
            List<String> tables = databaseUtils.getAllTableNames();
            return ResponseEntity.ok(tables);
        } catch (SQLException e) {
            logger.error("Failed to get table names", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 获取表结构
     */
    @GetMapping("/tables/{tableName}/structure")
    public ResponseEntity<DatabaseUtils.TableInfo> getTableStructure(@PathVariable String tableName) {
        try {
            DatabaseUtils.TableInfo tableInfo = databaseUtils.getTableStructure(tableName);
            return ResponseEntity.ok(tableInfo);
        } catch (SQLException e) {
            logger.error("Failed to get table structure for: " + tableName, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 生成表的DDL
     */
    @GetMapping("/tables/{tableName}/ddl")
    public ResponseEntity<Map<String, String>> getTableDDL(@PathVariable String tableName) {
        try {
            String ddl = databaseUtils.generateTableDDL(tableName);
            Map<String, String> result = new HashMap<>();
            result.put("tableName", tableName);
            result.put("ddl", ddl);
            return ResponseEntity.ok(result);
        } catch (SQLException e) {
            logger.error("Failed to generate DDL for table: " + tableName, e);
            Map<String, String> errorResult = new HashMap<>();
            errorResult.put("error", "生成DDL失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResult);
        }
    }
    
    /**
     * 执行SQL查询
     */
    @PostMapping("/query")
    public ResponseEntity<Map<String, Object>> executeQuery(@RequestBody Map<String, String> request) {
        String sql = request.get("sql");
        
        if (sql == null || sql.trim().isEmpty()) {
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("error", "SQL语句不能为空");
            return ResponseEntity.badRequest().body(errorResult);
        }
        
        // 验证SQL安全性
        if (!queryService.validateSqlSafety(sql)) {
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("error", "不安全的SQL语句，只允许SELECT查询");
            return ResponseEntity.badRequest().body(errorResult);
        }
        
        try {
            List<Map<String, Object>> results = databaseUtils.executeQuery(sql);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", results);
            response.put("rowCount", results.size());
            response.put("sql", sql);
            
            return ResponseEntity.ok(response);
        } catch (SQLException e) {
            logger.error("Failed to execute query: " + sql, e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("error", "查询执行失败: " + e.getMessage());
            errorResult.put("sql", sql);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResult);
        }
    }
    
    /**
     * 获取查询建议
     */
    @GetMapping("/suggestions")
    public ResponseEntity<List<String>> getQuerySuggestions() {
        try {
            List<String> suggestions = queryService.getQuerySuggestions();
            return ResponseEntity.ok(suggestions);
        } catch (Exception e) {
            logger.error("Failed to get query suggestions", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
