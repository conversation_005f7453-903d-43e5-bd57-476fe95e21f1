package com.chatcharge.controller;

import com.chatcharge.service.NaturalLanguageQueryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;

/**
 * 自然语言查询控制器
 */
@RestController
@RequestMapping("/api/query")
public class QueryController {
    
    private static final Logger logger = LoggerFactory.getLogger(QueryController.class);
    
    @Autowired
    private NaturalLanguageQueryService queryService;
    
    /**
     * 处理自然语言查询
     */
    @PostMapping("/natural-language")
    public Mono<ResponseEntity<NaturalLanguageQueryService.QueryResult>> processNaturalLanguageQuery(
            @RequestParam String question,
            @RequestParam(defaultValue = "DeepSeek-V3") String model) {
        
        if (question == null || question.trim().isEmpty()) {
            NaturalLanguageQueryService.QueryResult errorResult = new NaturalLanguageQueryService.QueryResult();
            errorResult.setSuccess(false);
            errorResult.setErrorMessage("问题不能为空");
            return Mono.just(ResponseEntity.badRequest().body(errorResult));
        }
        
        return queryService.processNaturalLanguageQuery(question, model)
            .map(result -> {
                if (result.isSuccess()) {
                    return ResponseEntity.ok(result);
                } else {
                    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
                }
            })
            .onErrorReturn(error -> {
                logger.error("Natural language query failed", error);
                NaturalLanguageQueryService.QueryResult errorResult = new NaturalLanguageQueryService.QueryResult();
                errorResult.setOriginalQuestion(question);
                errorResult.setSuccess(false);
                errorResult.setErrorMessage("查询处理失败: " + error.getMessage());
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResult);
            });
    }
    
    /**
     * 验证SQL查询安全性
     */
    @PostMapping("/validate-sql")
    public ResponseEntity<Map<String, Object>> validateSql(@RequestBody Map<String, String> request) {
        String sql = request.get("sql");
        
        Map<String, Object> result = new HashMap<>();
        
        if (sql == null || sql.trim().isEmpty()) {
            result.put("valid", false);
            result.put("message", "SQL语句不能为空");
            return ResponseEntity.badRequest().body(result);
        }
        
        boolean isValid = queryService.validateSqlSafety(sql);
        result.put("valid", isValid);
        
        if (isValid) {
            result.put("message", "SQL语句安全");
        } else {
            result.put("message", "不安全的SQL语句，只允许SELECT查询");
        }
        
        return ResponseEntity.ok(result);
    }
}
