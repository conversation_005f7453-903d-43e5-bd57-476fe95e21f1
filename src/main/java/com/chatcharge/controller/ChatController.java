package com.chatcharge.controller;

import com.chatcharge.config.LlmModelConfig;
import com.chatcharge.model.ChatMessage;
import com.chatcharge.model.ChatRequest;
import com.chatcharge.service.ChatHistoryService;
import com.chatcharge.service.LlmService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 聊天控制器
 */
@RestController
@RequestMapping("/api/chat")
public class ChatController {
    
    private static final Logger logger = LoggerFactory.getLogger(ChatController.class);
    
    @Autowired
    private LlmService llmService;
    
    @Autowired
    private ChatHistoryService chatHistoryService;
    
    /**
     * 发送聊天消息
     */
    @PostMapping
    public Mono<ResponseEntity<Map<String, Object>>> sendMessage(@RequestBody ChatRequest request) {
        return llmService.sendChatRequest(request)
            .map(response -> {
                Map<String, Object> result = new HashMap<>();
                result.put("success", true);
                result.put("content", response.getContent());
                result.put("thinkingContent", response.getThinkingContent());
                result.put("modelName", response.getModelName());
                result.put("timestamp", response.getTimestamp());
                
                return ResponseEntity.ok(result);
            })
            .onErrorReturn(error -> {
                logger.error("Chat request failed", error);
                Map<String, Object> errorResult = new HashMap<>();
                errorResult.put("success", false);
                errorResult.put("error", error.getMessage());
                
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResult);
            });
    }
    
    /**
     * 获取聊天历史
     */
    @GetMapping("/history/{sessionId}")
    public ResponseEntity<List<ChatMessage>> getChatHistory(@PathVariable String sessionId) {
        try {
            List<ChatMessage> history = chatHistoryService.getChatHistory(sessionId);
            return ResponseEntity.ok(history);
        } catch (Exception e) {
            logger.error("Failed to get chat history", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 清除聊天历史
     */
    @DeleteMapping("/history/{sessionId}")
    public ResponseEntity<Map<String, Object>> clearChatHistory(@PathVariable String sessionId) {
        try {
            chatHistoryService.clearHistory(sessionId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "History cleared successfully");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("Failed to clear chat history", e);
            
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("error", e.getMessage());
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResult);
        }
    }
    
    /**
     * 获取所有会话ID
     */
    @GetMapping("/sessions")
    public ResponseEntity<Set<String>> getAllSessions() {
        try {
            Set<String> sessions = chatHistoryService.getAllSessionIds();
            return ResponseEntity.ok(sessions);
        } catch (Exception e) {
            logger.error("Failed to get sessions", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 导出聊天历史
     */
    @GetMapping("/export/{sessionId}")
    public ResponseEntity<byte[]> exportChatHistory(@PathVariable String sessionId) {
        try {
            List<ChatMessage> history = chatHistoryService.getChatHistory(sessionId);
            
            // 生成CSV内容
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            OutputStreamWriter writer = new OutputStreamWriter(baos, StandardCharsets.UTF_8);
            
            // 写入BOM以支持Excel正确显示中文
            baos.write(0xEF);
            baos.write(0xBB);
            baos.write(0xBF);
            
            // 写入CSV标题
            writer.write("时间,角色,模型,内容,思考内容\n");
            
            // 写入数据
            for (ChatMessage message : history) {
                writer.write(String.format("\"%s\",\"%s\",\"%s\",\"%s\",\"%s\"\n",
                    message.getTimestamp().toString(),
                    message.getRole().name(),
                    message.getModelName() != null ? message.getModelName() : "",
                    escapeCsv(message.getContent()),
                    escapeCsv(message.getThinkingContent())
                ));
            }
            
            writer.flush();
            writer.close();
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", 
                "chat_history_" + sessionId + ".csv");
            
            return ResponseEntity.ok()
                .headers(headers)
                .body(baos.toByteArray());
                
        } catch (IOException e) {
            logger.error("Failed to export chat history", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 获取可用模型列表
     */
    @GetMapping("/models")
    public ResponseEntity<List<LlmModelConfig.ModelConfig>> getAvailableModels() {
        try {
            List<LlmModelConfig.ModelConfig> models = llmService.getAvailableModels();
            return ResponseEntity.ok(models);
        } catch (Exception e) {
            logger.error("Failed to get available models", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 获取默认模型
     */
    @GetMapping("/default-model")
    public ResponseEntity<Map<String, String>> getDefaultModel() {
        try {
            String defaultModel = llmService.getDefaultModel();
            Map<String, String> result = new HashMap<>();
            result.put("defaultModel", defaultModel);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("Failed to get default model", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * CSV字段转义
     */
    private String escapeCsv(String field) {
        if (field == null) {
            return "";
        }
        
        // 转义双引号并用双引号包围包含特殊字符的字段
        if (field.contains("\"") || field.contains(",") || field.contains("\n") || field.contains("\r")) {
            return field.replace("\"", "\"\"");
        }
        
        return field;
    }
}
