package com.chatcharge.controller;

import com.chatcharge.util.DatabaseUtils;
import com.chatcharge.util.ExcelUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Excel文件处理控制器
 */
@RestController
@RequestMapping("/api/excel")
public class ExcelController {
    
    private static final Logger logger = LoggerFactory.getLogger(ExcelController.class);
    
    @Autowired
    private ExcelUtils excelUtils;
    
    @Autowired
    private DatabaseUtils databaseUtils;
    
    /**
     * 导入Excel文件到数据库
     */
    @PostMapping("/import")
    public ResponseEntity<Map<String, Object>> importExcel(
            @RequestParam("file") MultipartFile file,
            @RequestParam("tableName") String tableName,
            @RequestParam(value = "sheetIndex", required = false) Integer sheetIndex) {
        
        Map<String, Object> result = new HashMap<>();
        
        // 验证文件
        if (file.isEmpty()) {
            result.put("success", false);
            result.put("error", "文件不能为空");
            return ResponseEntity.badRequest().body(result);
        }
        
        if (!excelUtils.isValidExcelFile(file.getOriginalFilename())) {
            result.put("success", false);
            result.put("error", "不支持的文件格式，只支持.xls和.xlsx文件");
            return ResponseEntity.badRequest().body(result);
        }
        
        // 验证表名
        if (tableName == null || tableName.trim().isEmpty()) {
            result.put("success", false);
            result.put("error", "表名不能为空");
            return ResponseEntity.badRequest().body(result);
        }
        
        // 清理表名
        tableName = tableName.trim().replaceAll("[^a-zA-Z0-9_\\u4e00-\\u9fa5]", "_");
        if (!tableName.matches("^[a-zA-Z_\\u4e00-\\u9fa5].*")) {
            tableName = "TABLE_" + tableName;
        }
        
        try {
            // 创建临时文件
            Path tempDir = Paths.get(System.getProperty("java.io.tmpdir"));
            Path tempFile = Files.createTempFile(tempDir, "excel_import_", ".tmp");
            
            try {
                // 保存上传的文件
                file.transferTo(tempFile.toFile());
                
                // 导入到数据库
                int recordCount = databaseUtils.importExcelToTable(
                    tempFile.toString(), 
                    tableName, 
                    sheetIndex
                );
                
                result.put("success", true);
                result.put("tableName", tableName);
                result.put("recordCount", recordCount);
                result.put("fileName", file.getOriginalFilename());
                result.put("fileSize", file.getSize());
                
                logger.info("Excel file imported successfully: {} -> {} ({} records)", 
                    file.getOriginalFilename(), tableName, recordCount);
                
                return ResponseEntity.ok(result);
                
            } finally {
                // 清理临时文件
                try {
                    Files.deleteIfExists(tempFile);
                } catch (IOException e) {
                    logger.warn("Failed to delete temp file: " + tempFile, e);
                }
            }
            
        } catch (Exception e) {
            logger.error("Failed to import Excel file: " + file.getOriginalFilename(), e);
            result.put("success", false);
            result.put("error", "导入失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }
    
    /**
     * 获取Excel文件信息
     */
    @PostMapping("/info")
    public ResponseEntity<Map<String, Object>> getExcelInfo(@RequestParam("file") MultipartFile file) {
        Map<String, Object> result = new HashMap<>();
        
        if (file.isEmpty()) {
            result.put("success", false);
            result.put("error", "文件不能为空");
            return ResponseEntity.badRequest().body(result);
        }
        
        if (!excelUtils.isValidExcelFile(file.getOriginalFilename())) {
            result.put("success", false);
            result.put("error", "不支持的文件格式");
            return ResponseEntity.badRequest().body(result);
        }
        
        try {
            // 创建临时文件
            Path tempDir = Paths.get(System.getProperty("java.io.tmpdir"));
            Path tempFile = Files.createTempFile(tempDir, "excel_info_", ".tmp");
            
            try {
                file.transferTo(tempFile.toFile());
                
                Map<String, Object> fileInfo = excelUtils.getExcelFileInfo(tempFile.toString());
                result.put("success", true);
                result.putAll(fileInfo);
                
                return ResponseEntity.ok(result);
                
            } finally {
                Files.deleteIfExists(tempFile);
            }
            
        } catch (Exception e) {
            logger.error("Failed to get Excel file info", e);
            result.put("success", false);
            result.put("error", "获取文件信息失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }
    
    /**
     * 预览Excel文件内容
     */
    @PostMapping("/preview")
    public ResponseEntity<Map<String, Object>> previewExcel(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "sheetIndex", defaultValue = "0") int sheetIndex,
            @RequestParam(value = "maxRows", defaultValue = "10") int maxRows) {
        
        Map<String, Object> result = new HashMap<>();
        
        if (file.isEmpty()) {
            result.put("success", false);
            result.put("error", "文件不能为空");
            return ResponseEntity.badRequest().body(result);
        }
        
        try {
            Path tempDir = Paths.get(System.getProperty("java.io.tmpdir"));
            Path tempFile = Files.createTempFile(tempDir, "excel_preview_", ".tmp");
            
            try {
                file.transferTo(tempFile.toFile());
                
                ExcelUtils.ExcelData excelData = excelUtils.readExcelSheet(tempFile.toString(), sheetIndex);
                
                // 限制预览行数
                List<List<Object>> rows = excelData.getRows();
                if (rows.size() > maxRows) {
                    rows = rows.subList(0, maxRows);
                    excelData.setRows(rows);
                }
                
                result.put("success", true);
                result.put("sheetName", excelData.getSheetName());
                result.put("headers", excelData.getHeaders());
                result.put("rows", excelData.getRows());
                result.put("totalRows", excelData.getRowCount());
                result.put("previewRows", rows.size());
                
                return ResponseEntity.ok(result);
                
            } finally {
                Files.deleteIfExists(tempFile);
            }
            
        } catch (Exception e) {
            logger.error("Failed to preview Excel file", e);
            result.put("success", false);
            result.put("error", "预览失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }
}
