package com.chatcharge.model;

import java.time.LocalDateTime;

/**
 * 聊天消息模型
 */
public class ChatMessage {
    
    public enum Role {
        USER, ASSISTANT, SYSTEM
    }
    
    private String id;
    private Role role;
    private String content;
    private String thinkingContent; // 用于存储思考内容
    private LocalDateTime timestamp;
    private String modelName;
    
    public ChatMessage() {
        this.timestamp = LocalDateTime.now();
    }
    
    public ChatMessage(Role role, String content) {
        this();
        this.role = role;
        this.content = content;
    }
    
    public ChatMessage(Role role, String content, String modelName) {
        this(role, content);
        this.modelName = modelName;
    }
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public Role getRole() {
        return role;
    }
    
    public void setRole(Role role) {
        this.role = role;
    }
    
    public String getContent() {
        return content;
    }
    
    public void setContent(String content) {
        this.content = content;
    }
    
    public String getThinkingContent() {
        return thinkingContent;
    }
    
    public void setThinkingContent(String thinkingContent) {
        this.thinkingContent = thinkingContent;
    }
    
    public LocalDateTime getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }
    
    public String getModelName() {
        return modelName;
    }
    
    public void setModelName(String modelName) {
        this.modelName = modelName;
    }
    
    public boolean hasThinkingContent() {
        return thinkingContent != null && !thinkingContent.trim().isEmpty();
    }
}
