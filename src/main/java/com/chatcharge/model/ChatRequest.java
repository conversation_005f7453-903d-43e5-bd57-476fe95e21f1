package com.chatcharge.model;

import java.util.Map;

/**
 * 聊天请求模型
 */
public class ChatRequest {
    
    private String message;
    private String modelName;
    private Map<String, Object> parameters;
    private String sessionId;
    
    public ChatRequest() {}
    
    public ChatRequest(String message, String modelName) {
        this.message = message;
        this.modelName = modelName;
    }
    
    // Getters and Setters
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public String getModelName() {
        return modelName;
    }
    
    public void setModelName(String modelName) {
        this.modelName = modelName;
    }
    
    public Map<String, Object> getParameters() {
        return parameters;
    }
    
    public void setParameters(Map<String, Object> parameters) {
        this.parameters = parameters;
    }
    
    public String getSessionId() {
        return sessionId;
    }
    
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }
}
