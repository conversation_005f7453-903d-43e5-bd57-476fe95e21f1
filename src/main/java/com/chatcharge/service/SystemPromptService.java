package com.chatcharge.service;

import com.chatcharge.util.DatabaseUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * System Prompt管理服务
 */
@Service
@ConfigurationProperties(prefix = "system-prompts")
public class SystemPromptService {
    
    private static final Logger logger = LoggerFactory.getLogger(SystemPromptService.class);
    
    @Autowired
    private DatabaseUtils databaseUtils;
    
    // 系统提示词配置
    private Map<String, String> systemPrompts = new HashMap<>();
    private Map<String, String> promptTemplates = new HashMap<>();
    private Map<String, Object> defaultConfig = new HashMap<>();
    
    /**
     * 获取系统提示词
     */
    public String getSystemPrompt(String promptType) {
        return systemPrompts.getOrDefault(promptType, systemPrompts.get("general-chat"));
    }
    
    /**
     * 获取提示词模板
     */
    public String getPromptTemplate(String templateName) {
        return promptTemplates.get(templateName);
    }
    
    /**
     * 生成数据库查询的组合提示词
     */
    public String generateDatabaseQueryPrompt(String userQuestion) {
        try {
            // 获取所有表结构
            StringBuilder tableStructures = new StringBuilder();
            List<String> tableNames = databaseUtils.getAllTableNames();
            
            for (String tableName : tableNames) {
                if (tableName.startsWith("CHAT_HISTORY") || tableName.startsWith("SYSTEM")) {
                    continue; // 跳过系统表
                }
                
                DatabaseUtils.TableInfo tableInfo = databaseUtils.getTableStructure(tableName);
                tableStructures.append("表名: ").append(tableName).append("\n");
                tableStructures.append("列信息:\n");
                
                for (DatabaseUtils.ColumnInfo column : tableInfo.getColumns()) {
                    tableStructures.append("  - ").append(column.getColumnName())
                                  .append(" (").append(column.getDataType()).append(")");
                    
                    if (column.isPrimaryKey()) {
                        tableStructures.append(" [主键]");
                    }
                    
                    if (!column.isNullable()) {
                        tableStructures.append(" [非空]");
                    }
                    
                    tableStructures.append("\n");
                }
                tableStructures.append("\n");
            }
            
            // 获取数据库助手的系统提示词
            String systemPrompt = getSystemPrompt("database-assistant");
            
            // 替换占位符
            String combinedPrompt = systemPrompt.replace("{table_structures}", tableStructures.toString());
            
            return combinedPrompt;
            
        } catch (SQLException e) {
            logger.error("Failed to generate database query prompt", e);
            return getSystemPrompt("general-chat");
        }
    }
    
    /**
     * 生成SQL查询提示词
     */
    public String generateSqlQueryPrompt(String userQuestion) {
        try {
            StringBuilder tableStructures = new StringBuilder();
            List<String> tableNames = databaseUtils.getAllTableNames();
            
            for (String tableName : tableNames) {
                if (tableName.startsWith("CHAT_HISTORY") || tableName.startsWith("SYSTEM")) {
                    continue;
                }
                
                String ddl = databaseUtils.generateTableDDL(tableName);
                tableStructures.append(ddl).append("\n\n");
            }
            
            String template = getPromptTemplate("sql-query-template");
            if (template == null) {
                template = "基于以下数据库表结构：\n\n{table_structures}\n\n用户问题：{user_question}\n\n请生成相应的SQL查询语句。";
            }
            
            return template.replace("{table_structures}", tableStructures.toString())
                          .replace("{user_question}", userQuestion);
            
        } catch (SQLException e) {
            logger.error("Failed to generate SQL query prompt", e);
            return "无法获取数据库结构信息，请检查数据库连接。用户问题：" + userQuestion;
        }
    }
    
    /**
     * 生成数据分析提示词
     */
    public String generateDataAnalysisPrompt(String userQuestion, List<Map<String, Object>> queryResults) {
        StringBuilder resultsText = new StringBuilder();
        
        if (queryResults != null && !queryResults.isEmpty()) {
            // 构建结果文本
            Map<String, Object> firstRow = queryResults.get(0);
            
            // 添加列标题
            resultsText.append("查询结果（共").append(queryResults.size()).append("条记录）：\n\n");
            resultsText.append("列名：").append(String.join(", ", firstRow.keySet())).append("\n\n");
            
            // 添加数据行（最多显示前10行）
            int maxRows = Math.min(10, queryResults.size());
            for (int i = 0; i < maxRows; i++) {
                Map<String, Object> row = queryResults.get(i);
                resultsText.append("第").append(i + 1).append("行：");
                for (Map.Entry<String, Object> entry : row.entrySet()) {
                    resultsText.append(entry.getKey()).append("=").append(entry.getValue()).append(", ");
                }
                resultsText.append("\n");
            }
            
            if (queryResults.size() > maxRows) {
                resultsText.append("... (还有").append(queryResults.size() - maxRows).append("条记录)\n");
            }
        } else {
            resultsText.append("查询结果为空。\n");
        }
        
        String template = getPromptTemplate("data-analysis-template");
        if (template == null) {
            template = "基于以下数据查询结果：\n\n{query_results}\n\n用户问题：{user_question}\n\n请分析这些数据并提供洞察。";
        }
        
        return template.replace("{query_results}", resultsText.toString())
                      .replace("{user_question}", userQuestion);
    }
    
    /**
     * 更新系统提示词
     */
    public void updateSystemPrompt(String promptType, String content) {
        systemPrompts.put(promptType, content);
        logger.info("Updated system prompt: {}", promptType);
    }
    
    /**
     * 获取所有可用的提示词类型
     */
    public Map<String, String> getAllSystemPrompts() {
        return new HashMap<>(systemPrompts);
    }
    
    /**
     * 获取所有可用的模板
     */
    public Map<String, String> getAllPromptTemplates() {
        return new HashMap<>(promptTemplates);
    }
    
    /**
     * 验证提示词长度
     */
    public boolean validatePromptLength(String prompt) {
        int maxLength = (Integer) defaultConfig.getOrDefault("max-prompt-length", 4000);
        return prompt != null && prompt.length() <= maxLength;
    }
    
    /**
     * 截断过长的提示词
     */
    public String truncatePrompt(String prompt) {
        int maxLength = (Integer) defaultConfig.getOrDefault("max-prompt-length", 4000);
        if (prompt != null && prompt.length() > maxLength) {
            return prompt.substring(0, maxLength - 3) + "...";
        }
        return prompt;
    }
    
    // Configuration Properties Setters
    public void setSystemPrompts(Map<String, String> systemPrompts) {
        this.systemPrompts = systemPrompts;
    }
    
    public void setPromptTemplates(Map<String, String> promptTemplates) {
        this.promptTemplates = promptTemplates;
    }
    
    public void setDefault(Map<String, Object> defaultConfig) {
        this.defaultConfig = defaultConfig;
    }
    
    // Getters
    public Map<String, String> getSystemPrompts() {
        return systemPrompts;
    }
    
    public Map<String, String> getPromptTemplates() {
        return promptTemplates;
    }
    
    public Map<String, Object> getDefault() {
        return defaultConfig;
    }
}
