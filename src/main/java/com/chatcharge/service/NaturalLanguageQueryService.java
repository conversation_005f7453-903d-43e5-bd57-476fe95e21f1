package com.chatcharge.service;

import com.chatcharge.model.ChatMessage;
import com.chatcharge.model.ChatRequest;
import com.chatcharge.security.SecurityUtils;
import com.chatcharge.util.DatabaseUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 自然语言查询服务
 */
@Service
public class NaturalLanguageQueryService {
    
    private static final Logger logger = LoggerFactory.getLogger(NaturalLanguageQueryService.class);
    
    // SQL提取的正则表达式
    private static final Pattern SQL_PATTERN = Pattern.compile(
        "```sql\\s*([^`]+)```|```\\s*([^`]+)```|SELECT\\s+[^;]+(?:;|$)", 
        Pattern.CASE_INSENSITIVE | Pattern.DOTALL
    );
    
    @Autowired
    private LlmService llmService;
    
    @Autowired
    private SystemPromptService systemPromptService;
    
    @Autowired
    private DatabaseUtils databaseUtils;
    
    @Autowired
    private SecurityUtils securityUtils;
    
    /**
     * 查询结果
     */
    public static class QueryResult {
        private String originalQuestion;
        private String generatedSql;
        private List<Map<String, Object>> data;
        private String analysis;
        private boolean success;
        private String errorMessage;
        private long executionTime;
        
        // Getters and Setters
        public String getOriginalQuestion() {
            return originalQuestion;
        }
        
        public void setOriginalQuestion(String originalQuestion) {
            this.originalQuestion = originalQuestion;
        }
        
        public String getGeneratedSql() {
            return generatedSql;
        }
        
        public void setGeneratedSql(String generatedSql) {
            this.generatedSql = generatedSql;
        }
        
        public List<Map<String, Object>> getData() {
            return data;
        }
        
        public void setData(List<Map<String, Object>> data) {
            this.data = data;
        }
        
        public String getAnalysis() {
            return analysis;
        }
        
        public void setAnalysis(String analysis) {
            this.analysis = analysis;
        }
        
        public boolean isSuccess() {
            return success;
        }
        
        public void setSuccess(boolean success) {
            this.success = success;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
        
        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }
        
        public long getExecutionTime() {
            return executionTime;
        }
        
        public void setExecutionTime(long executionTime) {
            this.executionTime = executionTime;
        }
    }
    
    /**
     * 处理自然语言查询
     */
    public Mono<QueryResult> processNaturalLanguageQuery(String question, String modelName) {
        long startTime = System.currentTimeMillis();
        
        return generateSqlFromQuestion(question, modelName)
            .flatMap(sql -> {
                QueryResult result = new QueryResult();
                result.setOriginalQuestion(question);
                result.setGeneratedSql(sql);
                
                try {
                    // 执行SQL查询
                    List<Map<String, Object>> data = databaseUtils.executeQuery(sql);
                    result.setData(data);
                    result.setSuccess(true);
                    
                    // 生成数据分析
                    return generateDataAnalysis(question, data, modelName)
                        .map(analysis -> {
                            result.setAnalysis(analysis);
                            result.setExecutionTime(System.currentTimeMillis() - startTime);
                            return result;
                        });
                    
                } catch (SQLException e) {
                    logger.error("Failed to execute generated SQL: {}", sql, e);
                    result.setSuccess(false);
                    result.setErrorMessage("SQL执行失败: " + e.getMessage());
                    result.setExecutionTime(System.currentTimeMillis() - startTime);
                    return Mono.just(result);
                }
            })
            .onErrorReturn(error -> {
                QueryResult result = new QueryResult();
                result.setOriginalQuestion(question);
                result.setSuccess(false);
                result.setErrorMessage("查询处理失败: " + error.getMessage());
                result.setExecutionTime(System.currentTimeMillis() - startTime);
                return result;
            });
    }
    
    /**
     * 从自然语言问题生成SQL
     */
    private Mono<String> generateSqlFromQuestion(String question, String modelName) {
        // 生成SQL查询提示词
        String prompt = systemPromptService.generateSqlQueryPrompt(question);
        
        // 创建聊天请求
        ChatRequest request = new ChatRequest(prompt, modelName);
        request.setSessionId("sql-generation-" + System.currentTimeMillis());
        
        // 设置特定参数以获得更准确的SQL生成
        Map<String, Object> params = new HashMap<>();
        params.put("temperature", 0.1); // 降低随机性
        params.put("maxTokens", 1000);
        request.setParameters(params);
        
        return llmService.sendChatRequest(request)
            .map(this::extractSqlFromResponse);
    }
    
    /**
     * 从LLM响应中提取SQL语句
     */
    private String extractSqlFromResponse(ChatMessage response) {
        String content = response.getContent();
        if (content == null || content.trim().isEmpty()) {
            throw new RuntimeException("LLM返回空响应");
        }
        
        // 尝试从代码块中提取SQL
        Matcher matcher = SQL_PATTERN.matcher(content);
        if (matcher.find()) {
            String sql = matcher.group(1);
            if (sql == null) {
                sql = matcher.group(2);
            }
            if (sql == null) {
                sql = matcher.group(0);
            }
            
            // 清理SQL语句
            sql = sql.trim();
            if (sql.endsWith(";")) {
                sql = sql.substring(0, sql.length() - 1);
            }
            
            // 安全检查
            sql = securityUtils.sanitizeSqlQuery(sql);
            
            if (sql.trim().isEmpty()) {
                throw new RuntimeException("无法从响应中提取有效的SQL语句");
            }
            
            return sql;
        }
        
        // 如果没有找到代码块，尝试查找SELECT语句
        Pattern selectPattern = Pattern.compile("(SELECT\\s+[^;]+)", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
        Matcher selectMatcher = selectPattern.matcher(content);
        if (selectMatcher.find()) {
            String sql = selectMatcher.group(1).trim();
            return securityUtils.sanitizeSqlQuery(sql);
        }
        
        throw new RuntimeException("无法从LLM响应中提取SQL语句: " + content);
    }
    
    /**
     * 生成数据分析
     */
    private Mono<String> generateDataAnalysis(String originalQuestion, List<Map<String, Object>> queryResults, String modelName) {
        // 生成数据分析提示词
        String prompt = systemPromptService.generateDataAnalysisPrompt(originalQuestion, queryResults);
        
        // 创建聊天请求
        ChatRequest request = new ChatRequest(prompt, modelName);
        request.setSessionId("data-analysis-" + System.currentTimeMillis());
        
        // 设置参数
        Map<String, Object> params = new HashMap<>();
        params.put("temperature", 0.3);
        params.put("maxTokens", 2000);
        request.setParameters(params);
        
        return llmService.sendChatRequest(request)
            .map(ChatMessage::getContent)
            .onErrorReturn("数据分析生成失败");
    }
    
    /**
     * 验证SQL查询的安全性
     */
    public boolean validateSqlSafety(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return false;
        }
        
        String upperSql = sql.toUpperCase().trim();
        
        // 只允许SELECT查询
        if (!upperSql.startsWith("SELECT")) {
            return false;
        }
        
        // 检查危险关键词
        String[] dangerousKeywords = {
            "DROP", "DELETE", "INSERT", "UPDATE", "ALTER", "CREATE", 
            "TRUNCATE", "EXEC", "EXECUTE", "UNION", "SCRIPT"
        };
        
        for (String keyword : dangerousKeywords) {
            if (upperSql.contains(keyword)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 获取数据库表信息摘要
     */
    public Map<String, Object> getDatabaseSummary() {
        Map<String, Object> summary = new HashMap<>();
        
        try {
            List<String> tableNames = databaseUtils.getAllTableNames();
            summary.put("tableCount", tableNames.size());
            summary.put("tableNames", tableNames);
            
            Map<String, Integer> tableRowCounts = new HashMap<>();
            for (String tableName : tableNames) {
                try {
                    List<Map<String, Object>> result = databaseUtils.executeQuery(
                        "SELECT COUNT(*) as row_count FROM " + tableName
                    );
                    if (!result.isEmpty()) {
                        Object count = result.get(0).get("ROW_COUNT");
                        tableRowCounts.put(tableName, count != null ? ((Number) count).intValue() : 0);
                    }
                } catch (Exception e) {
                    logger.warn("Failed to get row count for table: {}", tableName, e);
                    tableRowCounts.put(tableName, 0);
                }
            }
            summary.put("tableRowCounts", tableRowCounts);
            
        } catch (SQLException e) {
            logger.error("Failed to get database summary", e);
            summary.put("error", "无法获取数据库信息: " + e.getMessage());
        }
        
        return summary;
    }
    
    /**
     * 获取查询建议
     */
    public List<String> getQuerySuggestions() {
        return List.of(
            "显示所有表的记录数量",
            "查询最近的数据记录",
            "统计各个分类的数量",
            "查找包含特定关键词的记录",
            "按时间排序显示数据",
            "计算数值字段的平均值",
            "查询重复的记录",
            "显示数据的分布情况"
        );
    }
}
