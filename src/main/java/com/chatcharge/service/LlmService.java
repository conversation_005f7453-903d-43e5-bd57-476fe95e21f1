package com.chatcharge.service;

import com.chatcharge.config.LlmModelConfig;
import com.chatcharge.model.ChatMessage;
import com.chatcharge.model.ChatRequest;
import com.chatcharge.security.SecurityUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.*;

/**
 * LLM API调用服务
 */
@Service
public class LlmService {
    
    private static final Logger logger = LoggerFactory.getLogger(LlmService.class);
    
    @Autowired
    private LlmModelConfig llmModelConfig;
    
    @Autowired
    private SecurityUtils securityUtils;
    
    @Autowired
    private ChatHistoryService chatHistoryService;
    
    private final WebClient webClient;
    private final ObjectMapper objectMapper;
    
    public LlmService() {
        this.webClient = WebClient.builder()
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .build();
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * 发送聊天请求到LLM API
     */
    public Mono<ChatMessage> sendChatRequest(ChatRequest request) {
        LlmModelConfig.ModelConfig modelConfig = llmModelConfig.getModelByName(request.getModelName());
        if (modelConfig == null) {
            return Mono.error(new IllegalArgumentException("Unknown model: " + request.getModelName()));
        }
        
        // 清理用户输入
        String sanitizedMessage = securityUtils.sanitizeUserInput(request.getMessage());
        
        // 获取历史对话
        List<ChatMessage> history = chatHistoryService.getChatHistory(request.getSessionId());
        
        // 构建请求体
        Map<String, Object> requestBody = buildRequestBody(modelConfig, sanitizedMessage, history, request.getParameters());
        
        return webClient.post()
                .uri(modelConfig.getApiUrl())
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + modelConfig.getApiKey())
                .bodyValue(requestBody)
                .retrieve()
                .bodyToMono(String.class)
                .timeout(Duration.ofMillis(llmModelConfig.getRequestTimeout()))
                .map(response -> parseResponse(response, modelConfig))
                .doOnNext(message -> {
                    // 保存用户消息和AI回复到历史记录
                    ChatMessage userMessage = new ChatMessage(ChatMessage.Role.USER, sanitizedMessage);
                    chatHistoryService.addMessage(request.getSessionId(), userMessage);
                    chatHistoryService.addMessage(request.getSessionId(), message);
                })
                .onErrorMap(this::handleError);
    }
    
    /**
     * 构建API请求体
     */
    private Map<String, Object> buildRequestBody(LlmModelConfig.ModelConfig modelConfig, 
                                                String message, 
                                                List<ChatMessage> history,
                                                Map<String, Object> customParams) {
        Map<String, Object> requestBody = new HashMap<>();
        
        // 构建消息列表
        List<Map<String, String>> messages = new ArrayList<>();
        
        // 添加历史消息
        for (ChatMessage historyMessage : history) {
            if (historyMessage.getRole() != ChatMessage.Role.SYSTEM) {
                Map<String, String> msg = new HashMap<>();
                msg.put("role", historyMessage.getRole().name().toLowerCase());
                msg.put("content", historyMessage.getContent());
                messages.add(msg);
            }
        }
        
        // 添加当前用户消息
        Map<String, String> userMessage = new HashMap<>();
        userMessage.put("role", "user");
        userMessage.put("content", message);
        messages.add(userMessage);
        
        requestBody.put("messages", messages);
        
        // 设置模型参数
        Map<String, Object> params = new HashMap<>(modelConfig.getDefaultParams());
        if (customParams != null) {
            params.putAll(customParams);
        }
        
        requestBody.putAll(params);
        
        // 根据不同的API设置特定参数
        if (modelConfig.getName().startsWith("DeepSeek")) {
            requestBody.put("model", "deepseek-chat");
        } else if (modelConfig.getName().startsWith("Qwen")) {
            requestBody.put("model", "qwen-turbo");
        } else if (modelConfig.getName().startsWith("GPT")) {
            requestBody.put("model", "gpt-4");
        } else if (modelConfig.getName().startsWith("Claude")) {
            requestBody.put("model", "claude-3-5-sonnet-20241022");
            requestBody.put("max_tokens", params.get("maxTokens"));
            requestBody.remove("maxTokens");
        }
        
        return requestBody;
    }
    
    /**
     * 解析API响应
     */
    private ChatMessage parseResponse(String response, LlmModelConfig.ModelConfig modelConfig) {
        try {
            JsonNode jsonResponse = objectMapper.readTree(response);
            String content = "";
            
            // 根据不同API的响应格式解析
            if (modelConfig.getName().startsWith("Claude")) {
                JsonNode contentArray = jsonResponse.get("content");
                if (contentArray != null && contentArray.isArray() && contentArray.size() > 0) {
                    content = contentArray.get(0).get("text").asText();
                }
            } else {
                // OpenAI格式 (GPT, DeepSeek, Qwen等)
                JsonNode choices = jsonResponse.get("choices");
                if (choices != null && choices.isArray() && choices.size() > 0) {
                    JsonNode message = choices.get(0).get("message");
                    if (message != null) {
                        content = message.get("content").asText();
                    }
                }
            }
            
            ChatMessage chatMessage = new ChatMessage(ChatMessage.Role.ASSISTANT, "", modelConfig.getName());
            
            // 处理思考内容（针对支持思考的模型）
            if (modelConfig.isSupportThinking() && securityUtils.isThinkingContent(content)) {
                String thinkingContent = securityUtils.extractThinkingContent(content);
                String finalContent = securityUtils.removeThinkingContent(content);
                
                chatMessage.setThinkingContent(securityUtils.sanitizeLlmResponse(thinkingContent, true));
                chatMessage.setContent(securityUtils.sanitizeLlmResponse(finalContent, false));
            } else {
                chatMessage.setContent(securityUtils.sanitizeLlmResponse(content, false));
            }
            
            return chatMessage;
            
        } catch (Exception e) {
            logger.error("Failed to parse LLM response", e);
            throw new RuntimeException("Failed to parse response from " + modelConfig.getName(), e);
        }
    }
    
    /**
     * 处理错误
     */
    private Throwable handleError(Throwable error) {
        logger.error("LLM API call failed", error);
        return new RuntimeException("Failed to get response from LLM: " + error.getMessage(), error);
    }
    
    /**
     * 获取可用的模型列表
     */
    public List<LlmModelConfig.ModelConfig> getAvailableModels() {
        return llmModelConfig.getModels();
    }
    
    /**
     * 获取默认模型
     */
    public String getDefaultModel() {
        return llmModelConfig.getDefaultModel();
    }
}
