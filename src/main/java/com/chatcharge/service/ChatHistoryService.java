package com.chatcharge.service;

import com.chatcharge.config.AppConfig;
import com.chatcharge.model.ChatMessage;
import com.chatcharge.security.SecurityUtils;
import com.opencsv.CSVReader;
import com.opencsv.CSVWriter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 聊天历史管理服务
 */
@Service
public class ChatHistoryService {
    
    private static final Logger logger = LoggerFactory.getLogger(ChatHistoryService.class);
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    @Autowired
    private AppConfig appConfig;
    
    @Autowired
    private SecurityUtils securityUtils;
    
    // 内存中的会话历史缓存
    private final Map<String, List<ChatMessage>> sessionHistories = new ConcurrentHashMap<>();
    
    /**
     * 添加消息到会话历史
     */
    public void addMessage(String sessionId, ChatMessage message) {
        if (sessionId == null || sessionId.trim().isEmpty()) {
            sessionId = "default";
        }
        
        List<ChatMessage> history = sessionHistories.computeIfAbsent(sessionId, k -> new ArrayList<>());
        
        // 限制历史记录数量
        int maxRecords = appConfig.getChat().getHistory().getMaxRecords();
        if (history.size() >= maxRecords * 2) { // 每轮对话包含用户消息和AI回复
            // 移除最早的一轮对话（用户消息+AI回复）
            if (history.size() >= 2) {
                history.remove(0);
                history.remove(0);
            }
        }
        
        message.setId(UUID.randomUUID().toString());
        history.add(message);
        
        // 异步保存到CSV文件
        saveToFile(sessionId, message);
    }
    
    /**
     * 获取会话历史
     */
    public List<ChatMessage> getChatHistory(String sessionId) {
        if (sessionId == null || sessionId.trim().isEmpty()) {
            sessionId = "default";
        }
        
        List<ChatMessage> history = sessionHistories.get(sessionId);
        if (history == null) {
            // 尝试从文件加载历史记录
            history = loadFromFile(sessionId);
            if (history != null) {
                sessionHistories.put(sessionId, history);
            } else {
                history = new ArrayList<>();
                sessionHistories.put(sessionId, history);
            }
        }
        
        return new ArrayList<>(history); // 返回副本
    }
    
    /**
     * 清除会话历史
     */
    public void clearHistory(String sessionId) {
        if (sessionId == null || sessionId.trim().isEmpty()) {
            sessionId = "default";
        }
        
        sessionHistories.remove(sessionId);
        
        // 清除文件中的记录
        clearFileHistory(sessionId);
    }
    
    /**
     * 获取所有会话ID
     */
    public Set<String> getAllSessionIds() {
        Set<String> sessionIds = new HashSet<>(sessionHistories.keySet());
        
        // 从文件系统中查找其他会话
        try {
            Path dataDir = Paths.get(appConfig.getDatabase().getDataDirectory());
            if (Files.exists(dataDir)) {
                Files.list(dataDir)
                    .filter(path -> path.getFileName().toString().startsWith("chat_history_") 
                                 && path.getFileName().toString().endsWith(".csv"))
                    .forEach(path -> {
                        String fileName = path.getFileName().toString();
                        String sessionId = fileName.substring("chat_history_".length(), fileName.length() - 4);
                        sessionIds.add(sessionId);
                    });
            }
        } catch (IOException e) {
            logger.warn("Failed to list session files", e);
        }
        
        return sessionIds;
    }
    
    /**
     * 保存消息到CSV文件
     */
    private void saveToFile(String sessionId, ChatMessage message) {
        try {
            Path dataDir = Paths.get(appConfig.getDatabase().getDataDirectory());
            Files.createDirectories(dataDir);
            
            String fileName = "chat_history_" + sessionId + ".csv";
            Path filePath = dataDir.resolve(fileName);
            
            boolean fileExists = Files.exists(filePath);
            
            try (FileWriter fileWriter = new FileWriter(filePath.toFile(), true);
                 CSVWriter csvWriter = new CSVWriter(fileWriter)) {
                
                // 如果文件不存在，写入标题行
                if (!fileExists) {
                    String[] header = {"id", "role", "content", "thinking_content", "timestamp", "model_name"};
                    csvWriter.writeNext(header);
                }
                
                // 写入消息数据
                String[] record = {
                    message.getId(),
                    message.getRole().name(),
                    securityUtils.escapeCsvField(message.getContent()),
                    securityUtils.escapeCsvField(message.getThinkingContent()),
                    message.getTimestamp().format(DATE_FORMATTER),
                    message.getModelName()
                };
                
                csvWriter.writeNext(record);
            }
            
        } catch (IOException e) {
            logger.error("Failed to save chat history to file", e);
        }
    }
    
    /**
     * 从CSV文件加载历史记录
     */
    private List<ChatMessage> loadFromFile(String sessionId) {
        try {
            Path dataDir = Paths.get(appConfig.getDatabase().getDataDirectory());
            String fileName = "chat_history_" + sessionId + ".csv";
            Path filePath = dataDir.resolve(fileName);
            
            if (!Files.exists(filePath)) {
                return null;
            }
            
            List<ChatMessage> messages = new ArrayList<>();
            
            try (FileReader fileReader = new FileReader(filePath.toFile());
                 CSVReader csvReader = new CSVReader(fileReader)) {
                
                String[] header = csvReader.readNext(); // 跳过标题行
                String[] record;
                
                while ((record = csvReader.readNext()) != null) {
                    if (record.length >= 6) {
                        ChatMessage message = new ChatMessage();
                        message.setId(record[0]);
                        message.setRole(ChatMessage.Role.valueOf(record[1]));
                        message.setContent(record[2]);
                        message.setThinkingContent(record[3]);
                        message.setTimestamp(LocalDateTime.parse(record[4], DATE_FORMATTER));
                        message.setModelName(record[5]);
                        
                        messages.add(message);
                    }
                }
            }
            
            // 限制加载的记录数量
            int maxRecords = appConfig.getChat().getHistory().getMaxRecords() * 2;
            if (messages.size() > maxRecords) {
                messages = messages.subList(messages.size() - maxRecords, messages.size());
            }
            
            return messages;
            
        } catch (Exception e) {
            logger.error("Failed to load chat history from file", e);
            return null;
        }
    }
    
    /**
     * 清除文件中的历史记录
     */
    private void clearFileHistory(String sessionId) {
        try {
            Path dataDir = Paths.get(appConfig.getDatabase().getDataDirectory());
            String fileName = "chat_history_" + sessionId + ".csv";
            Path filePath = dataDir.resolve(fileName);
            
            if (Files.exists(filePath)) {
                Files.delete(filePath);
            }
            
        } catch (IOException e) {
            logger.error("Failed to clear chat history file", e);
        }
    }
    
    /**
     * 导出聊天历史
     */
    public void exportHistory(String sessionId, String exportPath) throws IOException {
        List<ChatMessage> history = getChatHistory(sessionId);
        
        try (FileWriter fileWriter = new FileWriter(exportPath);
             CSVWriter csvWriter = new CSVWriter(fileWriter)) {
            
            // 写入标题行
            String[] header = {"timestamp", "role", "model", "content", "thinking_content"};
            csvWriter.writeNext(header);
            
            // 写入数据
            for (ChatMessage message : history) {
                String[] record = {
                    message.getTimestamp().format(DATE_FORMATTER),
                    message.getRole().name(),
                    message.getModelName() != null ? message.getModelName() : "",
                    securityUtils.escapeCsvField(message.getContent()),
                    securityUtils.escapeCsvField(message.getThinkingContent())
                };
                csvWriter.writeNext(record);
            }
        }
    }
}
