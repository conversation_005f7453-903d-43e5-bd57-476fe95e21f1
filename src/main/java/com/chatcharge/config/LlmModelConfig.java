package com.chatcharge.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Map;

@Configuration
@ConfigurationProperties(prefix = "llm")
public class LlmModelConfig {
    
    private List<ModelConfig> models;
    private String defaultModel;
    private int requestTimeout = 60000;
    private int maxRetries = 3;
    
    public static class ModelConfig {
        private String name;
        private String displayName;
        private String apiUrl;
        private String apiKey;
        private Map<String, Object> defaultParams;
        private boolean supportThinking = false;
        
        // Getters and Setters
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public String getDisplayName() {
            return displayName;
        }
        
        public void setDisplayName(String displayName) {
            this.displayName = displayName;
        }
        
        public String getApiUrl() {
            return apiUrl;
        }
        
        public void setApiUrl(String apiUrl) {
            this.apiUrl = apiUrl;
        }
        
        public String getApiKey() {
            return apiKey;
        }
        
        public void setApiKey(String apiKey) {
            this.apiKey = apiKey;
        }
        
        public Map<String, Object> getDefaultParams() {
            return defaultParams;
        }
        
        public void setDefaultParams(Map<String, Object> defaultParams) {
            this.defaultParams = defaultParams;
        }
        
        public boolean isSupportThinking() {
            return supportThinking;
        }
        
        public void setSupportThinking(boolean supportThinking) {
            this.supportThinking = supportThinking;
        }
    }
    
    // Getters and Setters
    public List<ModelConfig> getModels() {
        return models;
    }
    
    public void setModels(List<ModelConfig> models) {
        this.models = models;
    }
    
    public String getDefaultModel() {
        return defaultModel;
    }
    
    public void setDefaultModel(String defaultModel) {
        this.defaultModel = defaultModel;
    }
    
    public int getRequestTimeout() {
        return requestTimeout;
    }
    
    public void setRequestTimeout(int requestTimeout) {
        this.requestTimeout = requestTimeout;
    }
    
    public int getMaxRetries() {
        return maxRetries;
    }
    
    public void setMaxRetries(int maxRetries) {
        this.maxRetries = maxRetries;
    }
    
    public ModelConfig getModelByName(String name) {
        return models.stream()
                .filter(model -> model.getName().equals(name))
                .findFirst()
                .orElse(null);
    }
}
