package com.chatcharge.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "app")
public class AppConfig {
    
    private ChatConfig chat = new ChatConfig();
    private DatabaseConfig database = new DatabaseConfig();
    
    public static class ChatConfig {
        private HistoryConfig history = new HistoryConfig();
        private SecurityConfig security = new SecurityConfig();
        
        public static class HistoryConfig {
            private int maxRecords = 10;
            private String csvFile = "./data/chat_history.csv";
            
            public int getMaxRecords() {
                return maxRecords;
            }
            
            public void setMaxRecords(int maxRecords) {
                this.maxRecords = maxRecords;
            }
            
            public String getCsvFile() {
                return csvFile;
            }
            
            public void setCsvFile(String csvFile) {
                this.csvFile = csvFile;
            }
        }
        
        public static class SecurityConfig {
            private boolean enableHtmlSanitization = true;
            private int maxMessageLength = 10000;
            
            public boolean isEnableHtmlSanitization() {
                return enableHtmlSanitization;
            }
            
            public void setEnableHtmlSanitization(boolean enableHtmlSanitization) {
                this.enableHtmlSanitization = enableHtmlSanitization;
            }
            
            public int getMaxMessageLength() {
                return maxMessageLength;
            }
            
            public void setMaxMessageLength(int maxMessageLength) {
                this.maxMessageLength = maxMessageLength;
            }
        }
        
        public HistoryConfig getHistory() {
            return history;
        }
        
        public void setHistory(HistoryConfig history) {
            this.history = history;
        }
        
        public SecurityConfig getSecurity() {
            return security;
        }
        
        public void setSecurity(SecurityConfig security) {
            this.security = security;
        }
    }
    
    public static class DatabaseConfig {
        private String dataDirectory = "./data/";
        private String excelImportDirectory = "./data/excel/";
        
        public String getDataDirectory() {
            return dataDirectory;
        }
        
        public void setDataDirectory(String dataDirectory) {
            this.dataDirectory = dataDirectory;
        }
        
        public String getExcelImportDirectory() {
            return excelImportDirectory;
        }
        
        public void setExcelImportDirectory(String excelImportDirectory) {
            this.excelImportDirectory = excelImportDirectory;
        }
    }
    
    public ChatConfig getChat() {
        return chat;
    }
    
    public void setChat(ChatConfig chat) {
        this.chat = chat;
    }
    
    public DatabaseConfig getDatabase() {
        return database;
    }
    
    public void setDatabase(DatabaseConfig database) {
        this.database = database;
    }
}
