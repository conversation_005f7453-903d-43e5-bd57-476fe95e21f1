package com.chatcharge;

import com.chatcharge.config.LlmModelConfig;
import com.chatcharge.security.SecurityUtils;
import com.chatcharge.service.ChatHistoryService;
import com.chatcharge.util.ExcelUtils;
import com.chatcharge.util.DatabaseUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
class ChatChargeApplicationTests {

    @Autowired
    private LlmModelConfig llmModelConfig;
    
    @Autowired
    private SecurityUtils securityUtils;
    
    @Autowired
    private ChatHistoryService chatHistoryService;
    
    @Autowired
    private ExcelUtils excelUtils;
    
    @Autowired
    private DatabaseUtils databaseUtils;

    @Test
    void contextLoads() {
        // 测试Spring上下文是否正确加载
        assertNotNull(llmModelConfig);
        assertNotNull(securityUtils);
        assertNotNull(chatHistoryService);
        assertNotNull(excelUtils);
        assertNotNull(databaseUtils);
    }
    
    @Test
    void testLlmModelConfig() {
        // 测试LLM模型配置
        assertNotNull(llmModelConfig.getModels());
        assertFalse(llmModelConfig.getModels().isEmpty());
        assertNotNull(llmModelConfig.getDefaultModel());
        
        // 测试获取模型
        LlmModelConfig.ModelConfig model = llmModelConfig.getModelByName("DeepSeek-V3");
        assertNotNull(model);
        assertEquals("DeepSeek V3", model.getDisplayName());
        assertTrue(model.isSupportThinking());
    }
    
    @Test
    void testSecurityUtils() {
        // 测试HTML转义
        String dangerous = "<script>alert('xss')</script>";
        String safe = securityUtils.sanitizeUserInput(dangerous);
        assertFalse(safe.contains("<script>"));
        
        // 测试思考内容检测
        String thinkingContent = "<thinking>这是思考过程</thinking>正常回复";
        assertTrue(securityUtils.isThinkingContent(thinkingContent));
        
        String extracted = securityUtils.extractThinkingContent(thinkingContent);
        assertEquals("这是思考过程", extracted);
        
        String cleaned = securityUtils.removeThinkingContent(thinkingContent);
        assertEquals("正常回复", cleaned);
        
        // 测试CSV转义
        String csvField = "包含,逗号和\"引号的内容";
        String escaped = securityUtils.escapeCsvField(csvField);
        assertTrue(escaped.startsWith("\""));
        assertTrue(escaped.endsWith("\""));
    }
    
    @Test
    void testExcelUtils() {
        // 测试Excel文件格式验证
        assertTrue(excelUtils.isValidExcelFile("test.xlsx"));
        assertTrue(excelUtils.isValidExcelFile("test.xls"));
        assertFalse(excelUtils.isValidExcelFile("test.txt"));
        assertFalse(excelUtils.isValidExcelFile("test.csv"));
    }
    
    @Test
    void testChatHistoryService() {
        // 测试会话ID获取
        assertNotNull(chatHistoryService.getAllSessionIds());
        
        // 测试获取空历史记录
        var history = chatHistoryService.getChatHistory("test-session");
        assertNotNull(history);
        assertTrue(history.isEmpty());
    }
}
