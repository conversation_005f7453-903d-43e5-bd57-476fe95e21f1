#!/bin/bash

# ChatCharge 启动脚本

echo "==================================="
echo "    ChatCharge LLM聊天应用启动"
echo "==================================="

# 检查Java版本
echo "检查Java环境..."
if ! command -v java &> /dev/null; then
    echo "错误: 未找到Java，请安装Java 17或更高版本"
    exit 1
fi

JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1)
if [ "$JAVA_VERSION" -lt 17 ]; then
    echo "错误: Java版本过低，需要Java 17或更高版本"
    exit 1
fi

echo "Java版本检查通过"

# 检查Maven
echo "检查Maven环境..."
if ! command -v mvn &> /dev/null; then
    echo "错误: 未找到Maven，请安装Maven 3.6或更高版本"
    exit 1
fi

echo "Maven环境检查通过"

# 创建数据目录
echo "创建数据目录..."
mkdir -p data
mkdir -p data/excel

# 设置环境变量提示
echo ""
echo "注意: 请确保已设置以下环境变量（可选）："
echo "  export DEEPSEEK_API_KEY=your-deepseek-api-key"
echo "  export QWEN_API_KEY=your-qwen-api-key"
echo "  export OPENAI_API_KEY=your-openai-api-key"
echo "  export CLAUDE_API_KEY=your-claude-api-key"
echo ""

# 编译项目
echo "编译项目..."
mvn clean compile -q

if [ $? -ne 0 ]; then
    echo "错误: 项目编译失败"
    exit 1
fi

echo "项目编译成功"

# 启动应用
echo ""
echo "启动ChatCharge应用..."
echo "访问地址: http://localhost:8080"
echo "按 Ctrl+C 停止应用"
echo ""

mvn spring-boot:run
